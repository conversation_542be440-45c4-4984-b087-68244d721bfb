<template>
  <div class="website-layout">
    <!-- 网站头部 -->
    <website-header></website-header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <router-view v-wechat-title="title"></router-view>
    </main>

    <!-- 网站底部 -->
    <website-footer></website-footer>
  </div>
</template>

<script>
import WebsiteHeader from "./components/WebsiteHeader";
import WebsiteFooter from "./components/WebsiteFooter";

export default {
  name: "WebsiteLayout",
  components: {
    WebsiteHeader,
    WebsiteFooter,
  },
  data() {
    return {};
  },
  computed: {
    title: function () {
      return this.$route.meta.title + "-" + process.env.VUE_APP_APPNAME;
    },
  },
  mounted() {
    console.log("WebsiteLayout mounted");
    // 在这里可以添加一些初始化逻辑
    document.title = this.title;
  },
  methods: {},
  watch: {
    $route: {
      handler() {
        // 路由变化时滚动到顶部
        window.scrollTo(0, 0);
      },
      immediate: true
    }
  }
};
</script>

<style scoped>
.website-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 200px); /* 减去头部和底部的高度 */
}
</style>
