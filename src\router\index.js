import Vue from "vue";
import Router from "vue-router";
import Layout from "@/views/layout/Layout";

Vue.use(Router);

// const originalPush = Router.prototype.push
// Router.prototype.push = function push(location, onResolve, onReject) {
//   if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
//   return originalPush.call(this, location).catch(err => err)
// }
function dynamicPropsFn(route) {
	return {
		dialogClose: function () {
			window.opener = null;
			window.open("", "self");
			window.close();
		}
	}
}
const Login = () => import('@/views/login/Login');
const Login2 = () => import('@/views/login/Login2');
export const constantRouterMap = [
	{
		path: "/login",
		name: "login",
		component: process.env.VUE_APP_Type == 1 ? Login : Login2,
		meta: { title: "登录", icon: "login" },
		hidden: true,
	},
	{
		path: "/redirect",
		name: "redirect",
		component: () => import("@/views/redirect"),
		hidden: true,
	},
	// 官网路由
	{
		path: "/",
		component: Layout,
		redirect: "/home",
		children: [
			{
				path: "home",
				name: "Home",
				component: () => import("@/views/website/Home"),
				meta: { title: "首页" },
			},
		],
	},
	{
		path: "/info-disclosure",
		component: Layout,
		redirect: "/info-disclosure/organization",
		meta: { title: "信息公开" },
		children: [
			{
				path: "",
				name: "InfoDisclosure",
				component: () => import("@/views/website/InfoDisclosure"),
				children: [
					{
						path: "organization",
						name: "Organization",
						component: () => import("@/views/website/Organization"),
						meta: { title: "组织机构" },
					},
					{
						path: "personnel-list",
						name: "PersonnelList",
						component: () => import("@/views/website/PersonnelList"),
						meta: { title: "全省纪检人员名单" },
					},
					{
						path: "division",
						name: "Division",
						component: () => import("@/views/website/Division"),
						meta: { title: "工作分工" },
					},
					{
						path: "leadership",
						name: "Leadership",
						component: () => import("@/views/website/Leadership"),
						meta: { title: "领导机构" },
					}
				]
			},
		],
	},
	{
		path: "/xfjb",
		component: Layout,
		children: [
			{
				path: "",
				name: "Xfjb",
				component: () => import("@/views/website/Xfjb"),
				meta: { title: "信访举报" },
			},
		],
	},
	{
		path: "/news",
		component: Layout,
		children: [
			{
				path: "",
				name: "News",
				component: () => import("@/views/website/News"),
				meta: { title: "新闻动态" },
			},
		],
	},
	{
		path: "/regulations",
		component: Layout,
		children: [
			{
				path: "",
				name: "Regulations",
				component: () => import("@/views/website/Regulations"),
				meta: { title: "法规制度" },
			},
		],
	},
	{
		path: "/showcase",
		component: Layout,
		children: [
			{
				path: "",
				name: "Showcase",
				component: () => import("@/views/website/Showcase"),
				meta: { title: "警示曝光" },
			},
		],
	},
	{
		path: "/culture",
		component: Layout,
		children: [
			{
				path: "",
				name: "Culture",
				component: () => import("@/views/website/Culture"),
				meta: { title: "廉洁文化" },
			},
		],
	},
	{
		path: "/report",
		component: Layout,
		children: [
			{
				path: "",
				name: "Report",
				component: () => import("@/views/website/Report"),
				meta: { title: "信访举报" },
			},
		],
	},
	{
		path: "/discipline-news",
		component: Layout,
		children: [
			{
				path: "",
				name: "DisciplineNews",
				component: () => import("@/views/website/DisciplineNews"),
				meta: { title: "新闻动态" }
			}
		]
	},
	{
		path: "/detail",
		component: Layout,
		children: [
			{
				path: "",
				name: "Detail",
				component: () => import("@/views/website/Detail"),
				meta: { title: "详情" },
			},
		],
	},
	{
		path: "/eightRegulations",
		name: "EightRegulations",
		component: () => import("@/views/website/EightRegulations"),
	},
	// 原有的管理系统路由
	{
		path: "/admin",
		component: Layout,
		name: "processTask",
		redirect: "/admin/mywork/processTask",
		children: [
			{
				path: "mywork/processTask",
				name: "processTask",
				component: () => import("@/views/mywork/processTask"),
				meta: { title: "我的待办", icon: "wodegongzuo" },
			},
			{
				path: "welcome",
				name: "welcome",
				component: () => import("@/views/welcome"),
				meta: { title: "首页", icon: "welcome" },
				hidden: true,
			},
		],
	},
	{
		path: "/workOrder",
		name: "workOrder",
		component: () => import("@/components/WorkOrder"),
		meta: { title: "工单详情", icon: 'home' },
		// props: dynamicPropsFn
	}
];
export default new Router({
	routes: constantRouterMap
});
