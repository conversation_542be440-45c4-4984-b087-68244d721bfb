<template>
  <div class="home-page">
    <!-- 通知公告滚动条 -->
    <div class="notice-bar">
      <div class="notice-container">
        <div class="notice-icon">
          <i class="el-icon-s-promotion"></i>
          <span class="notice-label">通知公告：</span>
        </div>
        <div
          class="notice-content"
          @mouseenter="pauseScroll"
          @mouseleave="resumeScroll"
        >
          <div class="notice-scroll" ref="noticeScroll">
            <div
              class="notice-item"
              v-for="(notice, index) in notices"
              :key="index"
              @click="handleNoticeClick(notice)"
            >
              {{ notice.notice }}
            </div>
            <!-- 重复一遍用于无缝滚动 -->
            <div
              class="notice-item"
              v-for="(notice, index) in notices"
              :key="'repeat-' + index"
              @click="handleNoticeClick(notice)"
            >
              {{ notice.notice }}
            </div>
          </div>
        </div>
        <div class="notice-more">
          <router-link to="/news?columnId=099&title=公告" class="more-btn"
            >更多></router-link
          >
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 左侧轮播图区域 -->
      <div class="carousel-section">
        <div class="swiper-container carousel-swiper">
          <div class="swiper-wrapper">
            <!--  -->
            <div
              class="swiper-slide"
              v-for="(item, index) in carouselItems"
              :key="index"
              @click="handleCarouselClick(item)"
            >
              <img
                :src="item.slideShowFile"
                alt="图片"
                class="carousel-slide-image"
              />
              <div class="swiper-name">
                {{ item.slideShowTitle }}
              </div>
            </div>
          </div>
          <!-- Add Pagination -->
          <div class="swiper-pagination"></div>
          <!-- Add Arrows -->
        </div>

        <!-- <el-carousel
          :interval="50000"
          :arrow="'hover'"
          indicator-position="outside"
          height="400px"
          class="news-carousel"
        >
          <el-carousel-item v-for="(item, index) in carouselItems" :key="index">
            <div class="carousel-item-content" @click="handleCarouselClick(item)">
              <img :src="item.slideShowFile" alt="图片" class="carousel-image" />
            </div>
          </el-carousel-item>
        </el-carousel> -->
      </div>

      <!-- 右侧新闻列表区域 -->
      <div class="news-section">
        <div class="news-header">
          <div class="news-header-bg">
            <h2 class="section-title">廉闻要论</h2>
            <router-link
              to="/news?columnId=005&title=廉闻要论"
              class="more-link"
              >更多></router-link
            >
          </div>
        </div>
        <div class="news-list">
          <!-- 加载状态 -->
          <div v-if="loadingStates.lianzhengYaowen" class="news-loading">
            <i class="el-icon-loading"></i>
            <span>加载中...</span>
          </div>

          <!-- 新闻列表 -->
          <div
            v-else
            class="news-item"
            v-for="(news, index) in newsList"
            :key="news.id || index"
            @click="handleNewsClick(news)"
          >
            <h4 class="news-title">{{ news.title }}</h4>
            <p class="news-summary" v-html="news.summary"></p>
          </div>

          <!-- 无数据提示 -->
          <div
            v-if="!loadingStates.lianzhengYaowen && newsList.length === 0"
            class="no-data"
          >
            <i class="el-icon-document"></i>
            <span>暂无数据</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 下方新闻信息区域 -->
    <div class="news-sections">
      <div class="news-sections-container">
        <!-- 左侧新闻栏目 -->
        <div class="left-news-columns">
          <!-- 纪检工作动态 -->
          <div class="news-column">
            <div class="column-header">
              <h3 class="column-title">纪检工作动态</h3>
              <router-link to="/discipline-news" class="column-more"
                >更多></router-link
              >
            </div>

            <!-- 公司动态部分 -->
            <div class="sub-section">
              <div class="sub-header">
                <div class="sub-header-content">
                  <span class="sub-title">• 公司动态</span>
                  <router-link
                    to="/news?columnId=004&title=分公司动态"
                    class="sub-more"
                    >更多></router-link
                  >
                </div>
              </div>
              <ul class="news-items">
                <li
                  class="news-item-simple"
                  v-for="(item, index) in companyNews"
                  :key="index"
                  @click="handleNewsClick(item)"
                >
                  <span class="news-dot">•</span>
                  <span class="news-text">{{ item.title }}</span>
                  <span class="news-date-simple">{{ item.date }}</span>
                </li>
                <li v-if="companyNews.length === 0" class="no-data">
                  <i class="el-icon-document"></i>
                  <span>暂无数据</span>
                </li>
              </ul>
            </div>

            <!-- 分公司动态部分 -->
            <div class="sub-section">
              <div class="sub-header">
                <div class="sub-header-content">
                  <span class="sub-title">• 分公司动态</span>
                  <router-link
                    to="/news?columnId=013001&title=分公司动态"
                    class="sub-more"
                    >更多></router-link
                  >
                </div>
              </div>
              <ul class="news-items">
                <li
                  class="news-item-simple"
                  v-for="(item, index) in branchNews"
                  :key="index"
                  @click="handleNewsClick(item)"
                >
                  <span class="news-dot">•</span>
                  <span class="news-text">{{ item.title }}</span>
                  <span class="news-date-simple">{{ item.date }}</span>
                </li>
                <li v-if="branchNews.length === 0" class="no-data">
                  <i class="el-icon-document"></i>
                  <span>暂无数据</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- 党廉工作动态 -->
          <div class="news-column morenews">
            <div class="column-header">
              <h3 class="column-title">党廉工作动态</h3>
              <router-link
                to="/news?columnId=027&title=党廉工作动态"
                class="column-more"
                >更多></router-link
              >
            </div>
            <ul class="news-items">
              <li
                class="news-item-simple"
                v-for="(item, index) in partyNews"
                :key="index"
                @click="handleNewsClick(item)"
              >
                <span class="news-dot">•</span>
                <span class="news-text">{{ item.title }}</span>
                <span class="news-date-simple">{{ item.date }}</span>
              </li>
              <li v-if="partyNews.length === 0" class="no-data">
                <i class="el-icon-document"></i>
                <span>暂无数据</span>
              </li>
            </ul>
          </div>

          <!-- 监督长廊 -->
          <div class="news-column">
            <div class="column-header">
              <h3 class="column-title">警钟长鸣</h3>
              <router-link to="/showcase?id=097" class="column-more"
                >更多></router-link
              >
            </div>
            <ul class="news-items">
              <li
                class="news-item-simple"
                v-for="(item, index) in supervisionNews"
                :key="index"
                @click="handleNewsClick(item)"
              >
                <span class="news-dot">•</span>
                <span class="news-text">{{ item.title }}</span>
                <span class="news-date-simple">{{ item.date }}</span>
              </li>
              <li v-if="supervisionNews.length === 0" class="no-data">
                <i class="el-icon-document"></i>
                <span>暂无数据</span>
              </li>
            </ul>
          </div>

          <!-- 业务讲堂 -->
          <div class="news-column morenews">
            <div class="column-header">
              <h3 class="column-title">纪检园地</h3>
              <router-link
                to="/news?columnId=028&title=纪检园地"
                class="column-more"
                >更多></router-link
              >
            </div>
            <ul class="news-items">
              <li
                class="news-item-simple"
                v-for="(item, index) in businessNews"
                :key="index"
                @click="handleNewsClick(item)"
              >
                <span class="news-dot">•</span>
                <span class="news-text">{{ item.title }}</span>
                <span class="news-date-simple">{{ item.date }}</span>
              </li>
              <li v-if="businessNews.length === 0" class="no-data">
                <i class="el-icon-document"></i>
                <span>暂无数据</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- 右侧功能区域 -->
        <div class="right-function-area">
          <!-- 警示教育专区 -->
          <div class="education-section">
            <div class="education-header">
              <h3 class="education-title">警示教育专区</h3>
              <div class="education-more" @click="gotoJingshi">更多></div>
            </div>
            <div class="education-banner" @click="gotoJingshi">
              <img
                src="@/assets/images/bdtp.jpg"
                alt="警示教育专区"
                class="education-banner-image"
              />
            </div>
            <ul class="education-items">
              <li
                class="education-item"
                v-for="(item, index) in educationNews"
                :key="index"
                @click="handleNewsClick(item)"
              >
                <span class="education-dot">•</span>
                <span class="education-text">{{ item.title }}</span>
                <span class="education-date">{{ item.date }}</span>
              </li>
              <li v-if="educationNews.length === 0" class="no-data">
                <i class="el-icon-document"></i>
                <span>暂无数据</span>
              </li>
            </ul>
          </div>

          <!-- 功能图片区域 -->
          <div class="function-images">
            <!-- 巡察工作 -->
            <div
              class="function-image-item"
              @click="handleFunctionClick('patrol')"
            >
              <img
                src="@/assets/images/xcgz.jpg"
                alt="巡察工作"
                class="function-image"
              />
            </div>

            <!-- 法规制度 -->
            <div
              class="function-image-item"
              @click="handleFunctionClick('law')"
            >
              <img
                src="@/assets/images/xgzd.jpg"
                alt="法规制度"
                class="function-image"
              />
            </div>
          </div>

          <!-- 廉洁文化 -->
          <div class="culture-section">
            <div class="culture-header">
              <h3 class="culture-title">廉洁文化</h3>
              <router-link to="/culture" class="culture-more"
                >更多></router-link
              >
            </div>
            <div class="culture-carousel-container">
              <div class="swiper-container culture-swiper">
                <div class="swiper-wrapper">
                  <!-- -->
                  <div
                    class="swiper-slide"
                    v-for="(item, index) in cultureImages"
                    :key="index"
                    @click="handleCultureClick(item)"
                  >
                    <img
                      :src="item.image"
                      :alt="item.title"
                      class="culture-slide-image"
                    />
                    <div class="culture-slide-title">{{ item.title }}</div>
                  </div>
                </div>
                <!-- Add Pagination -->
                <!-- Add Arrows -->
              </div>
            </div>
          </div>

          <!-- 纪法课堂 -->
          <div class="law-class-section">
            <div class="law-class-header">
              <h3 class="law-class-title">纪法课堂</h3>
              <router-link
                to="/news?columnId=029&title=纪法课堂"
                class="law-class-more"
                >更多></router-link
              >
            </div>
            <ul class="law-class-items">
              <li
                class="law-class-item"
                v-for="(item, index) in lawClassNews"
                :key="index"
                @click="handleNewsClick(item)"
              >
                <span class="law-class-dot">•</span>
                <span class="law-class-text">{{ item.title }}</span>
                <span class="law-class-date">{{ item.date }}</span>
              </li>
              <li v-if="lawClassNews.length === 0" class="no-data">
                <i class="el-icon-document"></i>
                <span>暂无数据</span>
              </li>
            </ul>
          </div>
           <div class="function-images">
            <!-- 纪检监察信访举报须知 -->
            <div
              class="function-image-item"
              @click="handleFunctionClick('report')"
            >
              <img
                src="@/assets/images/jjjb.jpg"
                alt="纪检监察信访举报须知"
                class="function-image"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 友情链接 -->
    <div class="friendship-links">
      <div class="friendship-container">
        <div class="friendship-header">
          <h3 class="friendship-title">友情链接</h3>
        </div>
        <div class="friendship-content">
          <div class="links-grid">
            <div
              v-for="(link, index) in friendshipLinks"
              :key="index"
              class="friendship-link"
              @click="handleLinkClick(link)"
            >
              {{ link.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getLianZhengYaoWen,
  getGongZuoDongTai,
  getGongZuoDongTaiFen,
  getDangLianGongZuoDongTai,
  getYewuJiangTang,
  getJiFaKetang,
  getJingShiJiaoYu,
  getGongGao,
  getCarousel,
  getJingZhongChangMing,
} from "@/api/home";
import { mapState } from "vuex";

import Swiper, { Navigation, Pagination, Autoplay } from "swiper";
import "swiper/swiper-bundle.min.css";
Swiper.use([Navigation, Pagination, Autoplay]);
let qLUrl =
  window.location.href.indexOf("*************:8088") != -1
    ? "http://*************:8088/hnjjwz"
    : "http://************:8088/hnjjwz";

export default {
  name: "HomePage",
  data() {
    return {
      // 通知公告数据
      notices: [],

      // 轮播图数据
      carouselItems: [],

      // 新闻列表数据（廉政要闻）
      newsList: [],

      // 各栏目新闻数据
      newsData: {
        lianzhengYaowen: [], // 廉政要闻 (columnId: 005)
        getGongZuoDongTai: [], // 工作动态 (columnId: 001)
        getGongZuoDongTaiFen: [],
        getDangLianGongZuoDongTai: [],
        getYewuJiangTang: [],
        getJiFaKetang: [],
        getJingShiJiaoYu: [],
        getGongGao: [],
        getCarousel: [],
        getJingZhongChangMing,
      },

      // 加载状态
      loadingStates: {
        lianzhengYaowen: false,
        getDangLianGongZuoDongTai: false,
        getGongZuoDongTaiFen: false,
        getDangLianGongZuoDongTai: false,
      },

      // 通知滚动定时器
      noticeTimer: null,
      scrollPosition: 0,

      // 公司动态
      companyNews: [],

      // 分公司动态
      branchNews: [],

      // 党廉工作动态
      partyNews: [],

      // 监督长廊
      supervisionNews: [],

      // 业务讲堂
      businessNews: [],

      // 警示教育专区
      educationNews: [],

      // 廉洁文化图片
      cultureImages: [
        {
          title: "故事",
          image: require("@/assets/images/lj_1.jpg"),
          url: "/culture/1",
          columnId: "012",
          id: "012001",
        },
        {
          title: "海报",
          image: require("@/assets/images/lj_2.jpg"),
          url: "/culture/2",
          columnId: "012",
          id: "012003",
        },
        {
          title: "漫画",
          image: require("@/assets/images/lj_3.jpg"),
          url: "/culture/3",
          columnId: "012",
          id: "012005",
        },
        {
          title: "视频",
          image: require("@/assets/images/lj_4.png"),
          url: "/culture/4",
          columnId: "012",
          id: "012004",
        },
        {
          title: "书画",
          image: require("@/assets/images/lj_5.jpg"),
          url: "/culture/5",
          columnId: "012",
          id: "012002",
        },
      ],

      // 纪法课堂
      lawClassNews: [],

      // 友情链接
      friendshipLinks: [
        { name: "中央纪委国家监委网站", url: "https://www.ccdi.gov.cn/" },
        { name: "中国纪检监察报", url: "https://jjjcb.ccdi.gov.cn/epaper/" },
        { name: "中国纪检监察杂志", url: "https://zgjjjc.ccdi.gov.cn/" },
        {
          name: "中央纪委国家监委驻国资委纪检监察组",
          url: "http://www.sasac.gov.cn/n2588020/n2877928/index.html",
        },
        { name: "河南省纪委监委网站", url: "https://www.hnsjw.gov.cn/sitesources/hnsjct/page_pc/index.html" },
        { name: "中国移动纪检监察网", url: "",type:'1' },
        { name: "清廉中国", url: "https://v.ccdi.gov.cn/special/qlzg2023/index.shtml" },
        { name: "学习强国", url: "https://www.xuexi.cn/" },
      ],
    };
  },
  computed: {
    ...mapState(["user"]),
  },

  mounted() {
    this.startNoticeScroll();
    this.loadAllNewsData();
    
  },

  beforeDestroy() {
    if (this.noticeTimer) {
      clearInterval(this.noticeTimer);
    }
  },

  methods: {
  initSwiper(){
    this.$nextTick(() => {
      new Swiper(".culture-swiper", {
        slidesPerView: 5,
        spaceBetween: 5,
        centeredSlides: false,
        loop: false,
        navigation: false,
        autoplay:false,
        // autoplay: {
        //   delay: 3000,
        //   disableOnInteraction: false,
        // },
        breakpoints: {
          0: { slidesPerView: 1 },
          600: { slidesPerView: 2 },
          900: { slidesPerView: 5 },
        },
      });
      new Swiper(".carousel-swiper", {
        spaceBetween: 0,
        centeredSlides: true,
        loop: true,
        navigation: false,
        pagination: {
          el: ".swiper-pagination",
          type: "bullets",
          clickable: true,
        },
        autoplay: {
          delay: 3000,
          disableOnInteraction: false,
        },
        observer: true,
        observeParents: true,
      });
    });

  },
    gotoJingshi() {
      // var url =
      //   qLUrl +
      //   "/html/eightRegulations/index.html?appcode=hnjjwz&loginuser=" +
      //   this.util.getRsa(this.user.user.username) +
      //   "&myFrom=OA";
      // window.open(url, "_blank");
      this.$router.push({
        path: "/eightRegulations",
      });
    },
    // 统一的数据处理方法
    processNewsData(articleList) {
      if (!Array.isArray(articleList)) return [];

      return articleList.map((item) => {
        // 处理摘要内容，去除HTML标签并限制长度
        let summary = this.util.extractPlainTextFromRichText(
          this.util.htmlDecode(item.mainBody)
        );

        return {
          id: item.id,
          title: this.util.htmlDecode(item.title),
          summary: summary,
          date: this.formatDate(item.createdTime),
          notice: item.announcementTitle,
          pmInsId: item.pmInsId,
          columnId: item.programaCode,
          url: `/detail`,
          slideShowFile: item.slideShowFile
            ? item.slideShowFile.length > 0
              ? item.slideShowFile[0].mobileFilePath
              : ""
            : "",
          slideShowTitle: item.slideShowTitle?item.slideShowTitle:"",
          // slideShowFile:'http://*************:8088/hnjjwz/sys/file/download/anonymous?id=F865247391770488832'
        };
      });
    },

    // 通用新闻加载方法
    async loadNewsByType(type, apiFunction, limit = 10) {
      try {
        this.loadingStates[type] = true;
        console.log(`开始加载${type}数据...`);

        const response = await apiFunction({
          page: 1,
          size: limit,
        });

        console.log(`${type}接口响应:`, response);

        if (response && response.data && response.data.articleList) {
          this.newsData[type] = this.processNewsData(response.data.articleList);
          console.log(`${type}数据加载成功，数量:`, this.newsData[type].length);
        } else {
          console.log(`${type}数据为空`);
          this.newsData[type] = [];
        }
      } catch (error) {
        console.error(`加载${type}数据失败:`, error);
        this.newsData[type] = [];
      } finally {
        this.loadingStates[type] = false;
      }
    },

    // 加载所有新闻数据
    async loadAllNewsData() {
      // 并行加载所有新闻数据
      await Promise.all([
        this.loadNewsByType("lianzhengYaowen", getLianZhengYaoWen, 5),
        this.loadNewsByType("getGongZuoDongTai", getGongZuoDongTai, 5),
        this.loadNewsByType("getGongZuoDongTaiFen", getGongZuoDongTaiFen, 5),
        this.loadNewsByType(
          "getDangLianGongZuoDongTai",
          getDangLianGongZuoDongTai,
          5
        ),
        this.loadNewsByType("getYewuJiangTang", getYewuJiangTang, 5),
        this.loadNewsByType("getJiFaKetang", getJiFaKetang, 5),
        this.loadNewsByType("getJingShiJiaoYu", getJingShiJiaoYu, 5),
        this.loadNewsByType("getGongGao", getGongGao, 5),
        this.loadNewsByType("getCarousel", getCarousel, 5),
        this.loadNewsByType("getJingZhongChangMing", getJingZhongChangMing, 5),
      ]);

      // 将廉政要闻数据同步到主新闻列表（用于首页右侧显示）
      this.newsList = this.newsData.lianzhengYaowen?.slice(0, 3);
      this.companyNews = this.newsData.getGongZuoDongTai?.slice(0, 4);
      this.branchNews = this.newsData.getGongZuoDongTaiFen?.slice(0, 4);
      this.partyNews = this.newsData.getDangLianGongZuoDongTai?.slice(0, 5);
      this.businessNews = this.newsData.getYewuJiangTang?.slice(0, 5);
      this.lawClassNews = this.newsData.getJiFaKetang?.slice(0, 4);
      this.educationNews = this.newsData.getJingShiJiaoYu?.slice(0, 4);
      this.notices = this.newsData.getGongGao?.slice(0, 5);
      this.carouselItems = this.newsData.getCarousel?.slice(0, 5);
      this.supervisionNews = this.newsData.getJingZhongChangMing?.slice(0, 4);
      console.log("所有新闻数据加载完成");
      this.initSwiper()
    },

    // 获取特定栏目的数据（供其他组件使用）
    getNewsDataByType(type) {
      return this.newsData[type] || [];
    },

    // 获取特定栏目的加载状态
    getLoadingStateByType(type) {
      return this.loadingStates[type] || false;
    },

    // 刷新特定栏目数据
    async refreshNewsData(type) {
      const apiMap = {
        lianzhengYaowen: getLianZhengYaoWen,
        getGongZuoDongTai: getGongZuoDongTai,
        getGongZuoDongTaiFen: getGongZuoDongTaiFen,
        getDangLianGongZuoDongTai: getDangLianGongZuoDongTai,
        getYewuJiangTang: getYewuJiangTang,
        getJiFaKetang: getJiFaKetang,
        getJingShiJiaoYu: getJingShiJiaoYu,
        getGongGao: getGongGao,
        getCarousel: getCarousel,
        getJingZhongChangMing: getJingZhongChangMing,
      };

      if (apiMap[type]) {
        await this.loadNewsByType(type, apiMap[type], 10);

        // 如果刷新的是廉政要闻，同步更新主新闻列表
        if (type === "lianzhengYaowen") {
          this.newsList = this.newsData.lianzhengYaowen.slice(0, 3);
        }
      }
    },

    // 开始通知滚动
    startNoticeScroll() {
      this.noticeTimer = setInterval(() => {
        this.scrollPosition += 1; // 每次移动1px
        this.scrollToNotice();
      }, 20); // 每50毫秒移动一次，实现平滑滚动
    },

    // 滚动到指定位置
    scrollToNotice() {
      const scrollElement = this.$refs.noticeScroll;
      if (scrollElement) {
        const totalWidth = scrollElement.scrollWidth / 2; // 总宽度的一半（因为重复了一遍）

        // 当滚动到一半时重置位置，实现无缝循环
        if (this.scrollPosition >= totalWidth) {
          this.scrollPosition = 0;
        }

        scrollElement.style.transform = `translateX(-${this.scrollPosition}px)`;
      }
    },

    // 暂停滚动
    pauseScroll() {
      if (this.noticeTimer) {
        clearInterval(this.noticeTimer);
        this.noticeTimer = null;
      }
    },

    // 恢复滚动
    resumeScroll() {
      if (!this.noticeTimer) {
        this.startNoticeScroll();
      }
    },

    // 处理通知点击
    handleNoticeClick(notice) {
      if (notice.url) {
        notice.url = `${notice.url}?id=${notice.id}&pmInsId=${notice.pmInsId}&columnId=099`;
        this.$router.push(notice.url);
      }
    },

    // 处理轮播图点击
    handleCarouselClick(item) {
      if (item.url) {
        item.url = `${item.url}?id=${item.id}&pmInsId=${item.pmInsId}&columnId=098`;
        this.$router.push(item.url);
      }
    },

    // 处理新闻点击
    handleNewsClick(news) {
      if (news.url) {
        news.url = `${news.url}?id=${news.id}&pmInsId=${news.pmInsId}&columnId=${news.columnId}`;
        this.$router.push(news.url);
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return "";

      let dateObj;
      if (typeof date === "string") {
        // 处理字符串格式的日期
        dateObj = new Date(date);
      } else if (date instanceof Date) {
        dateObj = date;
      } else {
        return "";
      }

      // 检查日期是否有效
      if (isNaN(dateObj.getTime())) {
        return "";
      }

      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, "0");
      const day = String(dateObj.getDate()).padStart(2, "0");
      return `${month}-${day}`;
    },

    // 处理功能按钮点击
    handleFunctionClick(type) {
      switch (type) {
        case "patrol":
          this.$router.push({
            path: "/news",
            query: {
              columnId: "018",
              title: "巡察工作",
            },
          });
          break;
        case "law":
          this.$router.push({
            path: "/news",
            query: {
              columnId: "017",
              title: "法规制度",
            },
          });
          break;
        case "report":
          this.$router.push({
            path: "/xfjb",
            
          });
          break;
        default:
          break;
      }
    },

    // 处理廉洁文化图片点击
    handleCultureClick(item) {
      item.url = `/culture?id=${item.id}&columnId=${item.columnId}`;
      this.$router.push(item.url);
    },

    // 处理友情链接点击
    handleLinkClick(link) {
      // 可以在这里添加统计或其他逻辑
      console.log("点击友情链接:", link.name);
      if(link.type){
        var url = 'http://iportal.ha.cmcc/HaSmapSso/bps_jjjcwz.jsp?a=1&amp;iv-user='+this.util.getRsa(this.user.user.username)
        window.open(url, "_blank");
      }else{
        window.open(link.url, "_blank");
      }
    },
  },
};
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 通知公告滚动条 */
.notice-bar {
  padding: 8px 0;
}

.notice-container {
  max-width: 1160px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 0 20px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.notice-icon {
  display: flex;
  align-items: center;
  color: #dc143c;
  font-weight: bold;
  margin-right: 15px;
  white-space: nowrap;
}

.notice-icon i {
  font-size: 18px;
  margin-right: 5px;
}

.notice-label {
  font-size: 14px;
}

.notice-content {
  flex: 1;
  height: 40px;
  overflow: hidden;
  position: relative;
}

.notice-scroll {
  display: flex;
  width: max-content; /* 宽度根据内容自动调整 */
  animation: none; /* 移除CSS动画，使用JS控制 */
}

.notice-item {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: color 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
  padding-right: 40px; /* 增加间距，让通知之间有足够空隙 */
  min-width: max-content; /* 确保文字完全显示 */
}

.notice-item:hover {
  color: #dc143c;
}

.notice-more {
  margin-left: 15px;
  white-space: nowrap;
}

.more-btn {
  color: #c90100;
  font-size: 12px;
  text-decoration: none;
  transition: color 0.3s ease;
}

.more-btn:hover {
  color: #a00;
}

/* 主要内容区域 */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 20px;
  padding: 10px 20px;
  align-items: stretch;
}
.carousel-section,
.news-section {
  flex: 1 1 0;
  min-width: 0;
  height: 400px;
  display: flex;
  flex-direction: column;
}
.carousel-section {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  justify-content: flex-start;
}
.carousel-swiper {
  position: relative;
  height: 100% !important;
  min-height: 0;
  width: 100%;
  overflow: hidden;
}
.carousel-swiper .swiper-slide {
  cursor: pointer;
  height: 100% !important;
  min-height: 0;
  width: 100%;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  transition: none !important;
}
.carousel-slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}
.swiper-name {
  position: absolute;
  text-align: center;
  bottom: 0;
  background: #33333321;
  transition: 0.3s opacity;
  transform: translate3d(0, 0, 0);
  z-index: 99;
  height: 50px;
  width: 100%;
  text-align: center;
  line-height: 40px;
}

/* 右侧新闻列表区域 */
.news-section {
  flex: 1;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 400px; /* 与轮播图保持相同高度 */
  display: flex;
  flex-direction: column;
}

.news-header {
  margin-bottom: 0;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.news-header-bg {
  background-image: linear-gradient(95deg, #ffeaea 0%, #ffffff 100%);
  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #ff8585;
}

.section-title {
  color: #c90100;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  position: relative;
  border-bottom: 3px solid #c90100;
  padding: 5px 0;
}

/* .section-title::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: white;
  border-radius: 2px;
} */

.more-link {
  color: #c90100;
  font-size: 14px;
  text-decoration: none;
  transition: color 0.3s ease;
}

.more-link:hover {
  color: #c90100;
}

.news-list {
  padding: 0 20px 20px;
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 如果内容过多，允许滚动 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 内容从顶部开始排列 */
}

.news-item {
  padding: 15px 0;
  border-bottom: 1px dashed #767676;
  cursor: pointer;
  transition: all 0.3s ease;
}

.news-item:last-child {
  border-bottom: none;
}

.news-item:hover {
  background-color: #f8f8f8;
  margin: 0 -20px;
  padding-left: 20px;
  padding-right: 20px;
}

.news-title {
  font-size: 16px;
  font-weight: bold;
  color: #000000;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
  padding: 0;
}

.news-item:hover .news-title {
  color: #dc143c;
}

.news-summary {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-date {
  font-size: 12px;
  color: #999;
  float: right;
}

/* 加载状态样式 */
.news-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #666;
  font-size: 14px;
}

.news-loading i {
  margin-right: 8px;
  font-size: 16px;
}

/* 无数据状态样式 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 15px;
  min-height: 120px;
}

.no-data i {
  margin-right: 8px;
  font-size: 16px;
}

/* 下方新闻信息区域 */
.news-sections {
  background-color: #f5f5f5;
  padding: 10px 0;
}

.news-sections-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 20px;
  padding: 0 20px;
  align-items: stretch;
  box-sizing: border-box;
}

/* 左侧新闻栏目 */
.left-news-columns {
  flex: 1 1 0;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.news-column {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 18px 18px 12px 18px;
  margin-bottom: 18px;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}

.column-header {
  background-image: linear-gradient(95deg, #ffeaea 0%, #ffffff 100%);
  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #ff8585;
}

.column-title {
  color: #c90100;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  border-bottom: 3px solid #c90100;
  padding: 5px 0;
}

.column-more {
  color: #c90100;
  font-size: 12px;
  text-decoration: none;
}

.column-more:hover {
  color: #c90100;
}

/* 子部分样式 */
.sub-section {
}

.sub-section:last-child {
  border-bottom: none;
}

.sub-header {
  padding: 0px 20px;
}
.sub-header-content {
  padding: 10px 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ffc310;
}

.sub-title {
  color: #c90100;
  font-size: 14px;
  font-weight: bold;
}

.sub-more {
  color: #717171;
  font-size: 12px;
  text-decoration: none;
}

.sub-more:hover {
  color: #a00;
}

.news-items {
  list-style: none;
  margin: 0;
  padding: 10px 15px 9px;
  min-width: 0;
  min-height: 149px;
}
.morenews .news-items{
  min-height: 181px;

}

.news-item-simple {
  display: flex;
  align-items: center;
  padding: 5px 0;
  /* border-bottom: 1px solid #f0f0f0; */
  cursor: pointer;
  transition: color 0.3s ease;
  min-width: 0;
  overflow: hidden;
}
.news-item-simple:nth-child(1) {
  /* padding-top: 15px; */
}

.news-item-simple:last-child {
  border-bottom: none;
}

.news-item-simple:hover {
  color: #dc143c;
}

.news-dot {
  color: #000;
  margin-right: 8px;
  font-weight: bold;
}

.news-text {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.news-date-simple {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
  white-space: nowrap;
}

/* 右侧功能区域 */
.right-function-area {
  flex: 1 1 0;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 警示教育专区 */
.education-section {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 15px 10px 0;
  min-height: 220px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}

.education-header {
  background-image: linear-gradient(95deg, #ffeaea 0%, #ffffff 100%);
  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #ff8585;
}

.education-title {
  color: #c90100;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  border-bottom: 3px solid #c90100;
  padding: 5px 0;
}

.education-more {
  cursor: pointer;
  color: #c90100;
  font-size: 12px;
  text-decoration: none;
}

.education-more:hover {
  color: #c90100;
}

.education-banner {
  cursor: pointer;

  padding: 0;
  overflow: hidden;
}

.education-banner-image {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
  padding: 15px 15px 0;
}

.education-items {
  list-style: none;
  margin: 0;
  padding: 15px;
  min-height: 160px;
}

.education-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  /* border-bottom: 1px solid #f0f0f0; */
  cursor: pointer;
  transition: color 0.3s ease;
}

.education-item:last-child {
  border-bottom: none;
}

.education-item:hover {
  color: #dc143c;
}

.education-dot {
  color: #000;
  margin-right: 8px;
  font-weight: bold;
}

.education-text {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.education-date {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
  white-space: nowrap;
}

.education-section .no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 15px;
  min-height: 80px;
}

/* 功能图片区域 */
.function-images {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0;
}

.function-image-item {
  cursor: pointer;
  padding: 0;
  background: none;
  border-radius: 0;
  box-shadow: none;
  display: block;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.function-image {
  width: 100%;
  object-fit: cover;
  border-radius: 0;
  background: none;
  display: block;
}

/* 廉洁文化 */
.culture-section {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 15px 10px 0;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}

.culture-header {
  background-image: linear-gradient(95deg, #ffeaea 0%, #ffffff 100%);
  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #ff8585;
}

.culture-title {
  color: #c90100;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  border-bottom: 3px solid #c90100;
  padding: 5px 0;
}

.culture-more {
  color: #c90100;
  font-size: 12px;
  text-decoration: none;
}

.culture-more:hover {
  color: #c90100;
}

.culture-carousel-container {
  padding: 15px 0;
  overflow: hidden;
  margin-top: 10px;
  min-height: 228px;
}
.culture-swiper {
  width: 100%;
  height: 198px;
  box-sizing: border-box;
  padding: 0;
}
.swiper-wrapper {
  /* 不要align-items:center，避免图片被压缩 */
}
.culture-swiper .swiper-slide {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  cursor: pointer;
  height: auto;
  margin: 0;
  transition: none !important;
  box-sizing: border-box;
  height: 180px;
  flex-direction: column !important;
  align-items: center !important;
}
.culture-slide-image {
  width: 95%;
  height: 180px;
  object-fit: cover;
  display: block;
  margin: 0 auto;
}
.culture-slide-title {
  font-size: 14px;
  margin-top: 8px;
}
.swiper-button-next,
.swiper-button-prev {
  display: none !important;
}
::v-deep.swiper-pagination{ 
  bottom: 0px!important;

}
::v-deep .swiper-pagination-bullet-active {
  background: #fff !important;
}

/* 纪法课堂 */
.law-class-section {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 15px 10px 0;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}

.law-class-header {
  background-image: linear-gradient(95deg, #ffeaea 0%, #ffffff 100%);
  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #ff8585;
}

.law-class-title {
  color: #c90100;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  border-bottom: 3px solid #c90100;
  padding: 5px 0;
}

.law-class-more {
  color: #c90100;
  font-size: 12px;
  text-decoration: none;
}

.law-class-more:hover {
  color: #c90100;
}

.law-class-items {
  list-style: none;
  margin: 0;
  padding: 15px;
  min-height: 161px;
}

.law-class-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  /* border-bottom: 1px solid #f0f0f0; */
  cursor: pointer;
  transition: color 0.3s ease;
}

.law-class-item:last-child {
  border-bottom: none;
}

.law-class-item:hover {
  color: #dc143c;
}

.law-class-dot {
  color: #000;
  margin-right: 8px;
  font-weight: bold;
}

.law-class-text {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.law-class-date {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
  white-space: nowrap;
}

.law-class-section .no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 15px;
  min-height: 80px;
}

/* 友情链接 */
.friendship-links {
  background-color: #f5f5f5;
  padding: 20px 0;
}

.friendship-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.friendship-header {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
}

.friendship-title {
  font-size: 18px;
  font-weight: bold;
  color: #dc143c;
  margin: 0;
  position: relative;
  display: inline-block;
  padding: 0 30px;
  background: #f5f5f5;
}

.friendship-title::before {
  content: "";
  position: absolute;
  top: 50%;
  left: -200px;
  right: calc(100% + 30px);
  height: 1px;
  background: #dc143c;
  transform: translateY(-50%);
}

.friendship-title::after {
  content: "";
  position: absolute;
  top: 50%;
  right: -200px;
  left: calc(100% + 30px);
  height: 1px;
  background: #dc143c;
  transform: translateY(-50%);
}

.friendship-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.links-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: space-between;
}

.links-grid::after {
  content: "";
  flex: auto;
}

.friendship-link {
  flex: 1 1 calc(25% - 12px);
  min-width: 180px;
  max-width: 280px;
  padding: 10px 15px;
  background: #f5f5f5;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.friendship-link:hover {
  background: #dc143c;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    padding: 15px;
    gap: 15px;
    align-items: normal; /* 移动端取消拉伸对齐 */
  }

  .notice-container {
    padding: 0 15px;
  }

  .more-btn {
    font-size: 11px;
  }

  .carousel-section {
    flex: none;
    height: 300px; /* 移动端调整高度 */
  }

  .news-section {
    flex: none;
    height: auto; /* 移动端自适应高度 */
  }

  .news-sections-container {
    flex-direction: column;
    padding: 0 15px;
  }

  .left-news-columns {
    flex: none;
  }

  .right-function-area {
    flex: none;
  }

  .function-images {
    gap: 12px;
  }

  .culture-carousel-container {
    padding: 12px;
  }

  .friendship-container {
    padding: 0 15px;
  }

  .friendship-title::before,
  .friendship-title::after {
    left: -100px;
    right: -100px;
  }

  .friendship-content {
    padding: 15px;
  }

  .links-grid {
    gap: 12px;
    justify-content: space-between;
  }

  .friendship-link {
    flex: 1 1 calc(50% - 6px);
    min-width: 150px;
    max-width: 250px;
    font-size: 13px;
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  .notice-icon {
    margin-right: 10px;
  }

  .notice-label {
    font-size: 13px;
  }

  .notice-item {
    font-size: 13px;
  }

  .more-btn {
    font-size: 10px;
  }

  .main-container {
    padding: 10px;
  }

  .news-header {
    padding: 15px 15px 10px;
  }

  .news-list {
    padding: 0 15px 15px;
  }

  .section-title {
    font-size: 16px;
  }

  .news-sections {
    padding: 15px 0;
  }

  .news-sections-container {
    padding: 0 10px;
  }

  .column-title,
  .education-title,
  .culture-title,
  .law-class-title {
    font-size: 14px;
  }

  .function-btn {
    height: 50px;
  }

  .btn-text {
    font-size: 14px;
  }

  .report-icon {
    width: 50px;
    height: 50px;
  }

  .report-title {
    font-size: 13px;
  }

  .report-desc {
    font-size: 11px;
  }

  .friendship-links {
    padding: 15px 0;
  }

  .friendship-container {
    padding: 0 10px;
  }

  .friendship-title {
    font-size: 16px;
  }

  .friendship-title::before,
  .friendship-title::after {
    display: none;
  }

  .friendship-content {
    padding: 15px 10px;
  }

  .links-grid {
    gap: 10px;
    justify-content: space-between;
  }

  .friendship-link {
    flex: 1 1 calc(100% - 5px);
    min-width: 120px;
    max-width: none;
    font-size: 12px;
    padding: 8px 10px;
  }
}

/* 新闻标题省略号样式 */
.news-text,
.news-title,
.column-title,
.education-title,
.culture-title,
.law-class-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

/* 多行省略（如需多行）可用如下样式：
.news-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}
*/

/* 轮播图分页点和左右按钮优化 */
.el-carousel__indicators {
  bottom: 18px !important;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  display: flex !important;
  justify-content: center;
  align-items: center;
}
.el-carousel__indicator {
  margin: 0 6px !important;
}
.el-carousel__button {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #eee;
  transition: background 0.2s;
}
.el-carousel__indicator.is-active .el-carousel__button {
  background: #c90100 !important;
}
.el-carousel__arrow {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.9);
  color: #c90100;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-size: 20px;
  transition: background 0.2s, color 0.2s;
  opacity: 0.85;
}
.el-carousel__arrow:hover {
  background: #c90100;
  color: #fff;
}
.el-carousel__arrow {
  display: flex !important;
  align-items: center;
  justify-content: center;
}
</style>
