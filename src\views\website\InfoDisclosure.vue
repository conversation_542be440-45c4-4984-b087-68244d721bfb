<template>
  <div class="info-disclosure-page">
    <div class="main-container">
      <!-- 左侧边栏 -->
      <aside class="sidebar">
        <div class="sidebar-header">
          <div class="sidebar-title">信息公开</div>
          <div class="sidebar-subtitle">公开透明·接受监督</div>
        </div>
        <div class="sidebar-section">
          <ul class="sidebar-menu">
            <li class="menu-item" :class="{ active: $route.path.includes('organization') }">
              <router-link to="/info-disclosure/organization" class="menu-link">
                <i class="el-icon-s-home menu-icon"></i>
                组织机构
              </router-link>
            </li>
            <li class="menu-item" :class="{ active: $route.path.includes('personnel-list') }">
              <router-link to="/info-disclosure/personnel-list" class="menu-link">
                <i class="el-icon-user-solid menu-icon"></i>
                人员名单
              </router-link>
            </li>
            <!-- <li class="menu-item" :class="{ active: $route.path.includes('division') }">
              <router-link to="/info-disclosure/division" class="menu-link">
                <i class="el-icon-s-operation menu-icon"></i>
                工作分工
              </router-link>
            </li>
            <li class="menu-item" :class="{ active: $route.path.includes('leadership') }">
              <router-link to="/info-disclosure/leadership" class="menu-link">
                <i class="el-icon-s-custom menu-icon"></i>
                领导机构
              </router-link>
            </li> -->
          </ul>
        </div>
      </aside>

      <!-- 主要内容 -->
      <main class="content-area">
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script>
export default {
  name: "InfoDisclosurePage",
  data() {
    return {};
  },
  created() {
    if (this.$route.path === '/info-disclosure' || this.$route.path === '/info-disclosure/') {
      this.$router.replace('/info-disclosure/organization');
    }
  }
};
</script>

<style scoped>
.info-disclosure-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 20px;
  padding: 20px;
  min-height: calc(100vh - 280px);
}

/* 左侧边栏 */
.sidebar {
  width: 250px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
  padding: 0;
  overflow: hidden;
}

.sidebar-header {
  background: #c90100;
  border-radius: 16px 16px 0 0;
  padding: 32px 0 18px 0;
  text-align: center;
}

.sidebar-title {
  color: #fff;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 2px;
  margin-bottom: 6px;
}

.sidebar-subtitle {
  color: #fff;
  font-size: 14px;
  opacity: 0.92;
  letter-spacing: 1px;
}

.sidebar-section {
  background: #fff;
  padding: 0 0 18px 0;
}

.sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 10px 0 0;
}

.menu-item {
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
}

.menu-link:hover {
  background-color: #f8f8f8;
  color: #c90100;
}

.menu-item.active .menu-link {
  background-color: #fff5f5;
  color: #c90100;
  font-weight: 500;
  position: relative;
}

.menu-item.active .menu-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: #c90100;
  border-radius: 2px;
}

.menu-icon {
  margin-right: 8px;
  font-size: 18px;
  vertical-align: middle;
  color: #c90100;
}

/* 主要内容区域 */
.content-area {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    padding: 15px;
  }
  
  .sidebar {
    width: 100%;
  }
}
</style>
