<template>
  <div class="leadership-main">
    <div class="leadership-card">
      <h2 class="leadership-title">领导机构</h2>
      <div class="leadership-divider"></div>
      <div class="leadership-desc-box">
        <div class="leadership-desc">
          河南移动纪委是河南移动党委的派驻机构，在河南移动党委和上级纪委的双重领导下，履行监督执纪问责职责。现将河南移动纪委领导机构成员及分工公布如下。
        </div>
      </div>
      <div class="leadership-grid">
        <div class="leader-card" v-for="(item, idx) in leaders" :key="idx">
          <div class="leader-avatar">
            <i class="el-icon-user"></i>
          </div>
          <div class="leader-name">{{ item.name }}</div>
          <div class="leader-title">{{ item.title }}</div>
          <div class="leader-icons">
            <i class="el-icon-phone-outline"></i>
            <i class="el-icon-message"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "LeadershipPage",
  data() {
    return {
      leaders: [
        { name: "张明", nameEn: "张明", title: "纪委书记" },
        { name: "李强", nameEn: "李强", title: "纪委副书记" },
        { name: "王静", nameEn: "王静", title: "纪委办公室主任" },
        { name: "赵伟", nameEn: "赵伟", title: "党风政风监督室主任" },
        { name: "刘芳", nameEn: "刘芳", title: "执纪审查室主任" },
        { name: "陈勇", nameEn: "陈勇", title: "案件审理室主任" }
      ]
    };
  }
};
</script>

<style scoped>
.leadership-main {
  background: #f5f5f5;
  min-height: 100vh;
}
.leadership-card {
  background: #fff;
  border-radius: 12px;
  max-width: 1100px;
  margin: 0 auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  padding: 32px 32px 40px 32px;
}
.leadership-title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
  margin-bottom: 8px;
}
.leadership-divider {
  height: 4px;
  width: 88px;
  background: #dc143c;
  border-radius: 2px;
  margin-bottom: 18px;
}
.leadership-desc-box {
  background: #fafbfc;
  border-radius: 4px;
  padding: 16px 20px;
  margin-bottom: 28px;
  border-left: 4px solid #dc143c;
}
.leadership-desc {
  color: #666;
  font-size: 15px;
  line-height: 1.8;
}
.leadership-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 28px 24px;
}
.leader-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  padding: 28px 18px 18px 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 0;
  transition: box-shadow 0.2s;
}
.leader-card:hover {
  box-shadow: 0 8px 24px rgba(220,20,60,0.13);
}
.leader-avatar {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: #f3f3f3;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  color: #ccc;
  margin-bottom: 12px;
}
.leader-name {
  font-size: 17px;
  color: #222;
  font-weight: 600;
  margin-bottom: 2px;
}
.leader-name-en {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 8px;
}
.leader-title {
  font-size: 14px;
  color: #888;
  margin-bottom: 18px;
}
.leader-icons {
  display: flex;
  gap: 18px;
  color: #bdbdbd;
  font-size: 18px;
}
@media (max-width: 900px) {
  .leadership-card {
    max-width: 98vw;
    padding: 18px 4vw 24px 4vw;
  }
  .leadership-grid {
    grid-template-columns: 1fr;
    gap: 18px;
  }
}
@media (max-width: 600px) {
  .leadership-grid {
    grid-template-columns: 1fr;
  }
}
</style> 