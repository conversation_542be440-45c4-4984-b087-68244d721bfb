import request from "@/assets/js/request";
import store from "@/store";
import util from "@/assets/js/public";
export function getDictList(dictType, isPublic) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/sys/dictValue/findDictValue`,
		contentType: "application/json;charset=UTF-8",
		data: {
			dictType: dictType
			//isPublic:(isPublic===undefined || isPublic)?true:false
		}
	});
}
export function getApiList(url, method, data) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}${url}`,
		contentType: "application/json;charset=UTF-8",
		method: `${method}`,
		data: data,
	});
}
export function uumsGetDictList(dictType) {
	return request({
		//url:'/uums/sys/dictValue/findDictValue/sso?loginuser='+util.encrypt(store.getters.user.username)+'&appcode='+process.env.VUE_APP_UUMSBASEURL,
		url: `/${process.env.VUE_APP_APPCODE}/uums/sys/dictvalue/findDictValue`,
		contentType: "application/json;charset=UTF-8",
		data: {
			dictType: dictType
		}
	});
}
export function testIo() {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/anonymous/testIo`,
		method: "get",
		responseType: "blob",
		contentType: "application/json;charset=UTF-8"
	});
}
export function getDictValueForUserType(dictType) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/tms/ManagementCostConfig/getDictValueForUserType?isInternalUserForCmcc=false`,
		contentType: "application/json;charset=UTF-8",
		data: { dictType: dictType }
	});
}
export function uploadProcessFiles(fd, data) {
	let url = util.toUrl(`/${process.env.VUE_APP_APPCODE}/sys/file/uploadProcessFiles/rest`, data || {});
	return request({
		url: url,
		contentType: "multipart/form-data",
		data: fd,
		catch: true
	});
}
export function findPOrgAndCityOrg() {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findPOrgAndCityOrg?appcode=${process.env.VUE_APP_APPCODE}`
	});
}
export function findSonByParentOrgId(orgCode) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findSonByParentOrgId?appcode=${process.env.VUE_APP_APPCODE}&orgCode=${orgCode}`,
		contentType: "application/json;charset=UTF-8"
	});
}

export function findOrgTreeFromCorp(val) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findOrgTreeFromCorp?appcode=${process.env.VUE_APP_APPCODE}`,
		contentType: "application/json;charset=UTF-8",
		data: {
			corpId: val
		}
	});
}
export function findUserOrgDim(params) {
	params.appcode = process.env.VUE_APP_APPCODE;
	let url = util.toUrl(`/${process.env.vue_app_appcode}/uums/sys/userinfo/findUserOrgDim`, params);
	return request({
		url: url
		//contentType:'application/json;charset=UTF-8',
		//data:params
	});
}
export function getCurrentUsersso(username) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/file/extend/download/sso?appcode=${process.env.VUE_APP_APPCODE}&from=oa&uid="${util.getRsa(username, true)}`
	});
}
export function getFileById(id) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/file/extend/findFileById?id=${id}`
	});
}
// 获取在线预览地址
export function getRedirectUrl(params) {
	return request({
		url: `/${process.env.VUE_APP_URL}/sys/file/getRedirectUrl/api?url=${encodeURIComponent(params)}`,
		contentType: 'application/json;charset=UTF-8',
	})
}

// 获取文件预览链接
export function getFilePreviewUrl(params) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/sys/file/getRedirectUrl?url=${params}`,
		contentType: 'application/json;charset=UTF-8'
	})
}



/**
 *  新增
 */
export function getAddFormSubmit(appCode, tableName, data) {
	return request({
		url: `/${appCode}/action/${tableName}/create`,
		contentType: 'application/json;charset=UTF-8',
		data: data,
	});
}


/**
 *  编辑
 */
export function getUpdateFormSubmit(appCode, tableName, data) {
	return request({
		url: `/${appCode}/action/${tableName}/update`,
		contentType: 'application/json;charset=UTF-8',
		data: data,
	});
}

/**
 *  查看详情
 */
export function getFormDetail(appCode, tableName, data) {
	return request({
		url: `/${appCode}/action/${tableName}/findOne`,
		contentType: 'application/json;charset=UTF-8',
		data: data,
	});
}

/**
 *  带附件的查看详情
 */
export function getFormDetailFile(appCode, tableName, data) {
	return request({
		url: `/${appCode}/action/${tableName}/findOneById`,
		contentType: 'application/json;charset=UTF-8',
		data: data,
	});
}

/**
 *  列表分页查询
 */
export function getTableList(appCode, tableName, data) {
	return request({
		// url: `/${appCode}/action/${tableName}/findAll?page=${data.page}&size=${data.size}`,
		url: `/${appCode}/action/${tableName}/findListByPage?page=${data.page}&size=${data.size}`,
		contentType: 'application/json;charset=UTF-8',
		data: data,
	});
}

/**
 *  列表不带分页查询
 */
export function getTableListNoPage(appCode, tableName, data) {
	return request({
		// url: `/${appCode}/action/${tableName}/findAll`,
		url: `/${appCode}/action/${tableName}/findListNoPage`,
		contentType: 'application/json;charset=UTF-8',
		data: data,
	});
}

/**
 *  删除单条数据
 */
export function getDelete(appCode, tableName, id) {
	return request({
		url: `/${appCode}/action/${tableName}/deleteById?id=${id}`,
		contentType: 'application/json;charset=UTF-8',
	});
}

/**
 *  删除多条数据
 */
export function getDeleteAll(appCode, tableName, data) {
	return request({
		url: `/${appCode}/action/${tableName}/deleteAllByIds`,
		contentType: 'application/json;charset=UTF-8',
		data: data
	});
}

/**
 *  列表导出
 */
export function exportExcel(appCode, tableName, params) {
	return request({
		url: `/${appCode}/action/${tableName}/exportExcel`,
		contentType: 'application/json;charset=UTF-8',
		responseType: 'blob',
		data: params,
	});
}


/**
 *  关联表单
 */
export function associationFind(params) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/${params.url}/findAll?source=PC&page=${params.page}&size=${params.size}`,
		contentType: "application/json; charset=utf-8",
		data: params
	});
}

// 展示类组件 查询接口
export function getDataInfoByFindAll(findAllUrl, params, query = false) {
	let url = `/${process.env.VUE_APP_APPCODE}${findAllUrl}`
	if (query) {
		url += util.jsonToQuery(params)
	}
	return request({
		url: url,
		contentType: 'application/json;charset=UTF-8'
	})
}

// api列表
// export function getApiDataByApi(url,params) {
// 	params.uid = util.encrypt(store.getters.user.username);
// 	return request({
// 		url: `/dataManager${url}?uid=${params.uid}&appcode=${params.appcode}&pageIndex=${params.page}&pageSize=${params.size}`,
// 		contentType: 'application/json;charset=UTF-8',
// 		data:params
// 	})
// }

// // api列表-无分页
// export function getApiDataByApiNoPage(url,params) {
// 	params.uid = util.encrypt(store.getters.user.username);
// 	return request({
// 		url: `/dataManager${url}?uid=${params.uid}&appcode=${params.appcode}`,
// 		contentType: 'application/json;charset=UTF-8',
// 		data:params
// 	})
// }


// 视图列表
export function getDataBySuperApi(params) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/dataManager/getDataBySuperApi?source=PC&page=${params.page}&size=${params.size}&apiType=post&url=${params.url}`,
		contentType: 'application/json;charset=UTF-8',
		data: params
	})
}

// 视图列表-无分页
export function getDataBySuperApiNoPage(params) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/dataManager/getDataBySuperApi?source=PC&apiType=post&url=${params.url}`,
		contentType: 'application/json;charset=UTF-8',
		data: params
	})
}

// 接口配置
export function getDataByApiPage(params) {
	return request({
		url: params.url,
		contentType: 'application/json;charset=UTF-8',
		data: params.params
	})
}

// 栏目导航
export function getSanouncement(params) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/newcolumn/findColumnByArticleID?columnId=${params.columnId}`,
		contentType: "application/json;charset=UTF-8",
		// data: params
	});
}

// 查看更多
export function getSanounList(params) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/newcolumn/findArticlePageByColumnId?columnId=${params.columnId}&page=${params.page}&size=${params.size}`,
		contentType: "application/json;charset=UTF-8",
		// data: params
	});
}

// 新闻详情
export function getSanounDetail(params) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/newcolumn/getDataArticleById?columnId=${params.columnId}&articleId=${params.articleId}`,
		contentType: "application/json;charset=UTF-8",
		// data: params
	});
}
// 公告列表
export function getSanounListGonggao(params) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/newcolumn/findAnnouncementPageByColumnId?columnId=${params.columnId}&page=${params.page}&size=${params.size}`,
		contentType: "application/json;charset=UTF-8",
		// data: params
	});
}

// 推荐
export function getSanounRecommend(params) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/newcolumn/recommendedArticle?pmInsId=${params.pmInsId}`,
		contentType: "application/json;charset=UTF-8",
		// data: params
	});
}