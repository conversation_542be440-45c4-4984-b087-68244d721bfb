<template>
  <div>
    <template :table="table">
      <div
        v-if="table.hasQueryForm || table.hasOtherQueryBtn || hasAddBtn.has"
        class="filter-container"
      >
        <div class="tableQueryDD tableQueryForm tableContent">
          <sb-el-form
            class="searchForm"
            v-if="table.hasQueryForm"
            :ref="queryForm"
            :form="table.queryForm"
            v-model="table.listQuery"
            :from="true"
            :on-ok="handleDoFun"
            @handleInputBtn="handleInputBtn"
          >
            <template
              v-for="item in table.queryForm.formItemList"
              v-slot:[item.template]="obj"
            >
              <slot
                v-if="item.template"
                :name="item.template"
                :obj="obj.obj"
              ></slot>
            </template>
          </sb-el-form>
          <div
            style="
              width: 200px;
              display: flex;
              flex-wrap: nowrap;
              justify-content: flex-end;
              margin-bottom: -2px;
            "
          >
            <div
              v-if="table.hasQueryForm"
              style="padding-bottom: 5px"
              class="tableQueryDD queryButton"
            >
              <el-button
                class="fl"
                @click="handleSearchList(1)"
                size="small"
                type="primary"
                >查询</el-button
              >
              <el-button
                class="fl ml15"
                @click="handleResetSearch()"
                size="small"
                type="primary"
                >重置</el-button
              >
            </div>
            <div
              v-if="hasAddBtn.has"
              class="tableQueryD ml15"
              style="padding-bottom: 5px"
            >
              <el-button
                class="fr"
                @click="handleAddList(hasAddBtn.item)"
                size="small"
                type="primary"
                v-text="hasAddBtn.text"
              ></el-button>
            </div>
          </div>
          <!--<el-input v-for="item in table.tr" v-if="item.query===true"
                        :key="item.id" v-model="table.listQuery[item.id]" class="w2 fl mr10" :size="item.size || 'small'" :placeholder="item.label || '请输入'"></el-input>-->
        </div>
      </div>
      <div
        v-if="table.hasOtherQueryBtn || false"
        :class="
          hasAddBtn.has
            ? 'tableQueryD tableQueryDD'
            : 'tableQueryD tableQueryDD tableOthers'
        "
        style="margin-left: 10px;display:flex"
      >
        <el-upload 
            v-if="table.hasUploadData"
            class="upload-demo ml10"
            :action="table.uploadData.action"
            :on-success="table.uploadData.isPopup?table.uploadData.isDialog?handleDialog:handlePopup:handleSuccess"
            :before-upload="handleProgress"
            multiple
            :limit="table.uploadData.limit"
            :show-file-list="false"
            ref="upload">
            <el-button :key="table.uploadData.id" :size="table.uploadData.size || 'small'" type="primary">{{table.uploadData.name}}</el-button>
        </el-upload>
        <el-button
          v-for="item in table.otherQueryBtn.data"
          v-if="!item.hide"
          :class="item.classname || 'ml10'"
          :key="item.id"
          :size="item.size || 'small'"
          :type="item.type || 'primary'"
          @click.stop="
            handleOperation(item.fun, item.name, item.id, 'otherQueryBtn')
          "
          >{{ item.name }}</el-button
        >
      </div>
      <div
        v-if="table.hasQueryForm || table.hasOtherQueryBtn || hasAddBtn.has"
        class="clearB"
      ></div>
      <div
        class="table-container"
        :class="table.padding ? 'nopadding' : ''"
        :style="
          table.hasQueryForm || table.hasOtherQueryBtn || hasAddBtn.has
            ? '' : 'margin:0px;'
        "
      >
        <el-table
          :key="tableL"
          v-loading.fullscreen.lock="table.loading"
          element-loading-text="请稍后，正在查询..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255, 255, 0.5)"
          :show-summary="table.hasShowSummary"
          :summary-method="table.getSummaries"
          :default-expand-all="table.defaultExpand || false"
          ref="SbElTable"
          :data="table.data"
          tooltip-effect="dark"
          :border="table.border"
          :highlight-current-row="table.highlightCurrentRow || false"
          :stripe="table.stripe"
          :row-key="table.rowKey || 'id'"
          :tree-props="table.treeProps || {}"
          style="width: 100%"
          :row-class-name="rowClassName"
          :span-method="objectSpanMethod"
          header-row-class-name="thClassName"
          @sort-change="handleSortChange"
          @selection-change="handleSelectionChange"
          @select="handleSelect"
          @select-all="handleSelectAll"
          @row-click="handleRowClick"
          @row-dblclick="handleRowDblClick"
          @cell-mouse-enter="handleCellMouseEnter"
          @cell-mouse-leave="handleCellMouseLeave"
        >
          <el-table-column
            v-if="table.hasSelect"
            type="selection"
            :selectable="table.selectFun"
            width="40"
            align="left"
            :fixed="table.fixed"
          ></el-table-column>
          <el-table-column
            v-if="table.showIndex"
            type="index"
            label="序号"
            width="60"
            align="left"
            :index="indexMethod"
            fixed
          ></el-table-column>
          <el-table-column type="expand" v-if="table.hasExpand" align="left">
            <template slot-scope="props">
              <el-form label-position="left" inline class="table-expand">
                <el-form-item
                  :label="item.label"
                  v-for="item in table.expands"
                  :key="item.id"
                >
                  <span>{{ props.row[item.prop] }}</span>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <template v-for="item in table.tr">
            <el-table-column
              v-if="
                ((item.show !== false && item.show !== 'template') ||
                  !item.show) &&
                (item.dictType || item.fun || item.dictFixed || item.showType || item.edits)
              "
              :label="item.label"
              :align="item.align || 'left'"
              :prop="item.prop"
              :class-name="item.className || ''"
              :sortable="item.sortable || false"
              :key="item.id"
              :fixed="item.fixed || false"
              :width="item.width || ''"
              :show-overflow-tooltip="!item.width ? true : false"
              :min-width="item.minWidth || '180'"
              :render-header="renderHeader"
            >
              <template v-if="item.dictType" v-slot="scope">{{
                dictionaryGetValue(
                  item.dictType,
                  dictionaryGetProp(scope.row, item.prop)
                )
              }}</template>
              <template v-else-if="item.currentDictType" v-slot="scope">{{
                dictionaryGetValue(
                  item.currentDictType,
                  dictionaryGetProp(scope.row, item.prop)
                )
              }}</template>
              <template v-else-if="item.fun" v-slot="scope">{{
                dictionaryGetValue(
                  item.fun,
                  dictionaryGetProp(scope.row, item.prop),
                  item.props
                )
              }}</template>
              <template v-else-if="item.dictFixed" v-slot="scope">{{
                dictionaryGetValue(
                  item.dictFixed,
                  dictionaryGetProp(scope.row, item.prop)
                )
              }}</template>
              <template v-else-if="item.showType" v-slot="scope">
                <el-image 
                  v-if="item.showType == 'img'"
                  style="width: 100px; height: 100px"
                  :src="scope.row[item.prop]" 
                  :preview-src-list="[scope.row[item.prop]]">
                </el-image>
                <el-progress v-else-if="item.showType == 'progress'" :text-inside="true" :stroke-width="20" :percentage="(scope.row[item.prop] - 0)"></el-progress>
                <span v-else-if="item.showType == 'whether'">{{scope.row[item.prop] == '1' ? '是' : '否'}}</span>
                <el-tag v-else-if="item.showType == 'status'">{{scope.row[item.prop]}}</el-tag>
                <span v-else-if="item.showType == 'custom'">
                  {{showTypeFunGetValue(item.showTypeFun,scope.row[item.prop])}}
                </span>
                <span v-else-if="item.showType == 'attachments'">
                  <div v-for="(info, i) in scope.row[item.prop]" :key="index" style="cursor: pointer;"  @click="handleFileToSee(info, i)">
                      {{info.fileName}}
                  </div>
                </span>
                <span v-else>{{scope.row[item.prop]}}</span>
              </template>
              <template v-else-if="item.edits" v-slot="scope">
                <div v-if="scope.row[item.prop+'Edit'] == true">
                  <!--下拉框-->
                  <el-select
                      v-if="item.editsType === 'select'"
                      :filterable="true"
                      @change="blurValueInput(scope.row, item.prop)"
                      placeholder="请选择"
                      :props="item.props || {value: 'value', label: 'name'}"
                      @blur="changeEdit(scope.row, item.prop)"
                  >
                      <el-option
                          v-for="(o, i) in item.options || ajaxOptions"
                          :key="o[item.props ? item.props.value : 'value']"
                          :label="o[item.props ? item.props.label : 'name']"
                          :value="o[item.props ? item.props.value : 'value']"
                          :disabled="o.disabled"
                      >
                          <template v-if="item.template" v-slot="scope">
                              <slot
                                  :name="item.template"
                                  :obj="{index: i, item}"
                              ></slot>
                          </template>
                      </el-option>
                  </el-select>
                  <!--日期(时间)选择框-->
                  <el-date-picker
                      v-else-if="item.editsType === 'date'"
                      :type="item.subtype || 'datetime'"
                      :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                      :format="item.viewFormat || item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                      v-model="scope.row[item.prop]"
                      placeholder="请选择"
                      range-separator="至"
                      start-placeholde="开始时间"
                      end-placeholde="结束时间"
                      :default-time="item.defaultTime ||(!item.subtype || (item.subtype && item.subtype.indexOf('time') > -1)
                        ? item.subtype == 'datetimerange'? ['00:00:00', '23:59:59']: '00:00:00': null)"
                      @change="blurValueInput(scope.row, item.prop)"
                      @blur="changeEdit(scope.row, item.prop)"
                  ></el-date-picker>
                  <el-input
                      v-model="scope.row[item.prop]"
                      v-else
                      size="mini"
                      @keyup.enter.native="$event.target.blur()"
                      @blur="blurValueInput(scope.row, item.prop)"
                  />
                </div>
                <span v-else>
                  <a style="color: #0f85cf">{{ scope.row[item.prop] }}</a>
                  <i class="el-icon-edit editBtn"
                    v-if="tableRowEditId === scope.row.id && tableColumnEditIndex === scope.column.id"
                    @click="editClick(scope.row, item.prop)"
                  ></i>
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="
                ((item.show !== false && item.show !== 'template') ||
                  !item.show) &&
                item.formatter
              "
              :label="item.label"
              :align="item.align || 'left'"
              :prop="item.prop"
              :class-name="item.className || ''"
              :sortable="item.sortable || false"
              :key="item.id"
              :fixed="item.fixed || false"
              :width="item.width || ''"
              :show-overflow-tooltip="!item.width ? true : false"
              :min-width="item.minWidth || '180'"
              :render-header="renderHeader"
            >
              <template v-if="item.formatter.type === 'date'" v-slot="scope">{{
                util.getDateFormat(
                  dictionaryGetProp(scope.row, item.prop),
                  item.formatter.fmtStr,
                  item.formatter.default
                )
              }}</template>
              <template v-else v-slot="scope">{{
                scope.row[item.prop] || item.formatter.default
              }}</template>
            </el-table-column>
            <el-table-column
              v-else-if="item.show !== false && item.show !== 'template'"
              :label="item.label"
              :align="item.align || 'left'"
              :prop="item.prop"
              :fixed="item.fixed || false"
              :class-name="item.className || ''"
              :sortable="item.sortable || false"
              :key="item.id"
              :width="item.width || ''"
              :show-overflow-tooltip="!item.width ? true : false"
              :min-width="item.minWidth || '150'"
              :render-header="renderHeader"
            >
            </el-table-column>
            <el-table-column
              v-else-if="item.show !== false && item.show === 'template'"
              :label="item.label"
              :align="item.align || 'left'"
              :class-name="item.className || ''"
              :sortable="item.sortable || false"
              :key="item.id"
              :fixed="item.fixed || false"
              :width="item.width || ''"
              :show-overflow-tooltip="!item.width ? true : false"
              :min-width="item.minWidth || '150'"
              :render-header="renderHeader"
            >
              <template v-slot="scope">
                <slot
                  :name="item.template || item.id"
                  :obj="handleScopeTem(scope, item)"
                ></slot>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            column-key="operation"
            :label="table.operation.label || '操作'"
            :align="table.operation.align || 'left'"
            :width="table.operation.width || '160'"
            :fixed="table.operation.fixed || false"
            :min-width="table.operation.minWidth || '160'"
            :class-name="table.operation.className || ''"
            v-if="handleHasOperation"
          >
            <template slot="header" slot-scope="scope">
              <span>操作</span>
              <span
                v-if="table.hasSetup"
                @click="setUpD = true"
                style="color: #627aa5"
                ><svg-icon icon-class="shezhi" class-name="optIcon"></svg-icon
              ></span>
            </template>
            <template v-slot="scope">
              <div
                v-for="(item, index) in table.operation.data"
                :key="item.id"
                class="inlineB"
              >
                <el-button
                  v-if="handleBtnShow(item, scope.row)"
                  :class="
                    index != table.operation.data.length
                      ? (item.classname || '') + ' mr10'
                      : item.classname || ''
                  "
                  :size="item.size || 'mini'"
                  :type="
                    item.type || (item.id === 'delete' ? 'danger' : 'primary')
                  "
                  @click.stop="
                    handleOperation(scope.$index, scope.row, item.id)
                  "
                  >{{ item.name }}</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="batch-operate-container" v-if="table.hasBatchOperate">
        <el-select
          :size="table.batchOperate.size || 'small'"
          v-model="table.batchOperate.operateType"
          placeholder="批量操作"
          class="w1"
        >
          <el-option
            v-for="item in table.batchOperate.list"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button
          :size="table.batchOperate.size || 'small'"
          class="ml15"
          @click="handleBatchOperate()"
          type="primary"
          >确定</el-button
        >
      </div>
      <div class="pagination-container" v-if="table.hasPagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total,sizes,prev,pager,next,jumper"
          :page-size="table.listQuery.size"
          :page-sizes="table.pageSizeList || [10, 20, 50]"
          :hide-on-single-page="hideSinglePage"
          :current-page="table.listQuery.page"
          :total="table.total"
          :pager-count="5"
        ></el-pagination>
      </div>
      <el-dialog
        v-if="table.addAndUpdateType === 'dialog'"
        v-dialogDrag
        :title="table.dialogTitle"
        :close-on-click-modal="false"
        :visible.sync="table.dialogVisible"
        append-to-body
        :width="table.form.width || '1000px'"
      >
        <sb-el-form
          :key="listFormL"
          :ref="listForm"
          :form="table.form"
          v-model="table.listFormModul"
          :from="true"
          :on-ok="handleDoFun"
          @handleInputBtn="handleInputBtn"
        >
          <template
            v-for="item in table.form.formItemList"
            v-slot:[item.template]="obj"
          >
            <slot
              v-if="item.template"
              :name="item.template"
              :obj="obj.obj"
            ></slot>
          </template>
        </sb-el-form>
        <!--<el-form :ref="listForm" :model="table.listFormModul" :rules="formRules" label-width="120px">
                    <el-form-item v-for="item in table.tr" v-if='item.addOrUpdate' :label="item.label" :prop="item.prop" :key="item.id">
                        <el-input v-model="table.listFormModul[item.id]" v-bind="$attrs" v-on="$listeners" autocomplete="off" @input="handleInput($event, item.key)"></el-input>
                    </el-form-item>
                </el-form>-->
        <span
          v-if="table.operation.nowBtn === false ? false : true"
          slot="footer"
          class="dialog-footer"
        >
          <template v-if="table.form.isCustomFooterBtn">
            <el-button
              @click="handleConfirm(listForm, item)"
              type="primary"
              v-for="item in table.form.customBtn"
              :key="item"
              size="small"
              >{{ item }}</el-button
            >
          </template>
          <el-button @click="table.dialogVisible = false" size="small"
            >取消</el-button
          >
          <el-button
            v-if="!table.form.isCustomFooterBtn"
            type="primary"
            @click="handleConfirm(listForm)"
            size="small"
            >确定</el-button
          >
        </span>
      </el-dialog>
      <el-dialog
        ref="setUpDialog"
        v-dialogDrag
        title="设置显示字段"
        :close-on-click-modal="false"
        :visible.sync="setUpD"
        append-to-body
        width="800px"
      >
        <div class="of_hidden">
          <div class="fieldLeft">
            <label
              v-for="(item, index) in table.setup"
              :key="item.id"
              class="field"
              @click="handleSetupDo(item, index)"
            >
              <a
                :id="item.id"
                :class="item.showF === false ? 'checkbox' : 'checkbox checked'"
                >✓</a
              ><span>{{ item.label }}</span>
            </label>
          </div>
          <div class="fieldRight">
            <h5 class="fieldTit">当前选定的字段</h5>
            <draggable v-model="table.setup">
              <transition-group>
                <label
                  v-if="item.showF"
                  v-for="(item, index) in table.setup"
                  :key="item.id"
                  class="fieldC"
                >
                  <span>{{ item.label }}</span>
                  <a @click="handleSetupDo(item, index)"
                    ><svg-icon icon-class="cuo" class-name="fr"></svg-icon
                  ></a>
                </label>
              </transition-group>
            </draggable>
          </div>
        </div>
        <span
          v-if="table.operation.nowBtn === false ? false : true"
          slot="footer"
          class="dialog-footer"
        >
          <el-button @click="setUpD = false" size="small">取消</el-button>
          <el-button type="primary" @click="handleSetUpConfirm()" size="small"
            >确定</el-button
          >
        </span>
      </el-dialog>
    </template>
  </div>
</template>
<script>
import { getDictList, uumsGetDictList } from "@/api/public";
import { createHash } from "crypto";
export default {
  name: "sbElTable",
  props: {
    table: {
      type: Object,
      default() {
        return {
          listFormS: true,
          hasMergeRowOrColumn: false, // 是否合并单元格
          loading: true, // 加载中动画
          hasShowSummary: false, // 是否显示表位合计行
          border: true, // 是否带有纵向边框，默认为false
          stripe: true, // 是否斑马条
          hasSelect: false, // 有无选中功能
          loadFilter: false, //是否前端分页
          loadFilterData: [], //前端分页总数据
          hasOperation: false, // 有无操作功能
          hasExpand: false, // 有无展开行功能
          hasPagination: false, //有无分布栏
          hasBatchOperate: false, //有无批量操作
          hasQueryForm: false, //有无查询条件
          hasFuzzyQuery: false, //是否模糊查询
          queryForm: {},
          queryBeforeSubmit: false,
          addAndUpdateType: "dialog", //新增修改页面以对话框形式打开还是以路由形式展示,默认为对话框
          modulName: "", //列表中文名称
          listFormModul: {}, //新增或者修改的数据结构
          multipleSelection: [], //多选选中数据存放变量
          dataDictionary: {}, //数据字典变量
          fixed: true, //固定复选框
          batchOperate: {
            operateType: null,
          },
          hasOtherQueryBtn: false,
          total: 0,
          listQuery: {
            size: 10,
            page: 1,
          },
          tr: [
            // 表头数据 —— className:列的class名
            {
              id: "1",
              label: "label",
              prop: "prop",
              className: "classname",
              minWidth: "80",
              show: true,
              // show有三种值：false隐藏当前列，true常规表格列，template自定义表格列
              //<template slot-scope="props" slot="example">
              //<a class="list-a" target="_blank" :href="'/#/bombscreen?mobile=' + props.obj.row.mobile">{{ props.obj.row.username }}</a>
              //</template>
            },
          ],
          data: [], // 表格数据 —— 如需添加行class，处理数据时则需要传入class名， class需为数组
          operation: {
            // 操作功能
            //label: '操作',                // 操作列的行首文字
            //width: '200',                // 操作列的宽度
            //className: '',               // 操作列的class名
            data: [
              // 操作列数据
              //{
              //  label: '通过',                // 按钮文字
              //  fun: 'handleSubmit',         // 点击按钮后触发的父组件事件
              //  size: 'mini',                // 按钮大小
              //  id: '1',                     // 按钮循环组件的key值
              //  classname: 'show'            // 按钮的类名
              //}
            ],
          },
          form: {
            isCustomFooterBtn: false, // 是否自定义表单底部按钮
          },
          expands: [
            // 展开行数据
            {
              id: "1",
              label: "label",
              prop: "prop",
            },
          ],
          getSummaries(param) {
            // 自定义表位合计行数据
            // *** 此函数传入param参数
            // console.log(param);
            // *** 最后返回一个数组，合计行会显示数组中的内容
            return [];
          },
        };
      },
    },
    onOk: {
      type: Function,
    },
  },
  data() {
    return {
      tableL: 1,
      setUpD: false,
      listFormL: 1,
      hideSinglePage: false,

      tableRowEditId: "", //点击的单元格在哪行
      tableColumnEditIndex: "", //点击的单元格在哪列
      ajaxOptions:[],
    };
  },
  computed: {
    hasAddBtn: function () {
      let re = false;
      let text = "新增";
      let obj = {};
      if (this.table.hasOperation) {
        for (let i in this.table.operation.data) {
          if (this.table.operation.data[i].id === "add") {
            re = true;
            text = this.table.operation.data[i].name || "新增";
            obj = this.table.operation.data[i];
          }
        }
      }
      return { has: re, text: text, item: obj };
    },
    listForm: function () {
      let fn = this.table.modulName.split("-")[0];
      return fn + "ListForm";
    },
    queryForm: function () {
      let fn = this.table.modulName.split("-")[0];
      return fn + "QueryForm";
    },
    handleHasOperation: function () {
      let n = true;
      if (this.table.hasOperation) {
        let dl = 0;
        for (let i in this.table.operation.data) {
          if (this.table.operation.data[i].id !== "add") dl++;
        }
        if (dl === 0) n = false;
      } else {
        n = false;
      }
      return n;
    },
  },
  updated() {
    //console.log("sbeltable---updated",JSON.stringify(this.table.multipleSelection));
    this.handleTableMultipSelection();
  },
  // mounted() {
  //     console.log(this.table.listQuery)
  // },
  methods: {
    handleProgress(){
        this.table.loading = true
    },
    handleSuccess(response,file) {
        let jsstr = response.slice(response.indexOf("result=")+7,response.lastIndexOf("<"));
        let responseData = JSON.parse(jsstr);
        if(responseData.status==200){
            this.$message({
                type:'success',
                message:'导入成功'
            })
            this.$emit('getList');
        }else{
            this.table.loading = false;
            this.$message.error(responseData.message);
        }
    },
    handleDialog(response,file) {
        // console.log(response);
        if(response.status==200){
            this.$emit('getDialog',response);
        }else{
            this.$message.error(response.message);
        }
    },
    handlePopup(response,file){
        let jsstr = response.slice(response.indexOf("result=")+7,response.lastIndexOf("<"));
        let responseData = JSON.parse(jsstr);
        if(responseData.status==200){
            this.table.loading = false;
            this.$emit('getPopup',responseData.data);
        }else{
            this.table.loading = false;
            this.$message.error(responseData.message);
        }
    },
    handleScopeTem(scope, item) {
      scope.item = item;
      return scope;
    },
    handleSetup() {
      let setup = [...this.table.tr];
      for (let i in setup) {
        setup[i].showF = setup[i].show;
        if (setup[i].showF === undefined || setup[i].showF === "template") {
          if (setup[i].showF === "template") setup[i].showType = "template";
          setup[i].showF = true;
        }
      }
      this.table.setup = setup;
      this.$emit("updateTableData", { setup: [...this.table.setup] });
    },
    handleSetupDo(item, index, type) {
      item.showF = !item.showF;
      this.table.setup[index] = item;
      this.$emit("updateTableData", { setup: [...this.table.setup] });
    },
    handleSetUpConfirm() {
      let tr = [];
      for (let i in this.table.setup) {
        let item = this.table.tr.find(
          (item) => item.id === this.table.setup[i].id
        );
        if (item) {
          if (this.table.setup[i].showType === "template") {
            item.show = this.table.setup[i].showF ? "template" : false;
          } else {
            item.show = this.table.setup[i].showF;
          }
          tr.push(item);
        }
      }
      this.table.tr = tr;
      this.$emit("updateTableData", { tr: [...this.table.tr] });
      ++this.tableL;
      this.setUpD = false;
    },
    indexMethod(index) {
      return (
        index + this.table.listQuery.size * (this.table.listQuery.page - 1) + 1
      );
    },
    handleTableMultipSelection() {
      this.handleClearSelection();
      // for(let i in this.table.multipleSelection){
      //     let index=this.table.data.findIndex(row => row[this.table.rowKey || 'id']===this.table.multipleSelection[i][this.table.rowKey || 'id']);
      //     if(index>-1) this.$refs.SbElTable.toggleRowSelection(this.table.data[index],true);
      // }
      // 2021.04.10发现上面写法有bug，无法选中多条 会报错，改为下面写法
      if (this.table.multipleSelection) {
        this.table.multipleSelection.forEach((row) => {
          // 不能自己自定义对象来设置选中（原因如下分析），那我可以从列表中找到需要选中的那个对象，然后把它拿过来作为选中的项就可以了
          this.$refs.SbElTable.toggleRowSelection(
            this.table.data.find((item) => {
              return row.id == item.id; // 注意这里寻找的字段要唯一，示例仅参考
            }),
            true
          );
        });
      }
    },
    showTypeFunGetValue(fun, value) {
      let funs = this.util.htmlDecode(fun)
      return eval(funs)
    },
    dictionaryGetValue(dictType, value, props) {
      //props一般用于非数据字典的转换
      let re = [];
      if (value || value === 0 || value === false) {
        let data = this.table[dictType];
        let vn = value;
        value = value.toString();
        if (value.indexOf(",") > -1) value = value.split(",");
        for (let i in this.table[dictType]) {
          if (typeof value === "string") {
            if (props) {
              let name = this.table[dictType][i][props.label || "name"];
              if (
                name !== "请选择" &&
                vn == this.table[dictType][i][props.value || "id"]
              )
                re.push(this.table[dictType][i][props.label || "name"]);
            } else {
              let name = this.table[dictType][i].name;
              if (name !== "请选择" && vn == this.table[dictType][i].value)
                re.push(this.table[dictType][i].name);
            }
          } else {
            if (props) {
              let name = this.table[dictType][i][props.label || "name"];
              for (let j in value) {
                if (
                  name !== "请选择" &&
                  value[j] == this.table[dictType][i][props.value || "id"]
                )
                  re.push(this.table[dictType][i][props.label || "name"]);
              }
            } else {
              let name = this.table[dictType][i].name;
              for (let j in value) {
                if (
                  name !== "请选择" &&
                  value[j] == this.table[dictType][i].value
                )
                  re.push(this.table[dictType][i].name);
              }
            }
          }
        }
      }
      return re.join(",");
    },
    handleBtnShow(item, row) {
      let re = item.id !== "add";
      // 如果show的值为true或false，直接赋值
      if (item.show == true || item.show == false) {
        re = item.show;
      } else {
        //否则对内容进行判断
        if (item.show) {
          // console.log(item,row)
          let sl = item.show.split(",");
          let l = 0;
          for (let i in sl) {
            let sli = sl[i].split("|");
            if (sli[1] === "true") sli[1] = true;
            if (sli[1] === "false") sli[1] = false;
            // let sliF=this.dictionaryGetProp(row,sli[0]);
            // if(sliF!==null && sliF==sli[1]) l++;
            let sliF = this.dictionaryGetProp(row, sli[0]);
            for (let j = 1; j < sli.length; j++) {
              if (sliF !== null && sliF == sli[j]) l++;
            }
          }
          re = l === sl.length ? true : false;
        }
      }
      if (item.hide) {
        let sl = item.hide.split(",");
        let l = 0;
        for (let i in sl) {
          let sli = sl[i].split("|");
          if (sli[1] === "true") sli[1] = true;
          if (sli[1] === "false") sli[1] = false;
          let sliF = this.dictionaryGetProp(row, sli[0]);
          if (sliF === null || sliF == sli[1]) l++;
        }
        re = l === sl.length ? false : true;
      }
      return re;
    },
    dictionaryGetProp(row, prop) {
      let value = row;
      if (prop.indexOf(".") > -1) {
        let propA = prop.split(".");
        for (let i in propA) {
          if (value !== null && value[propA[i]]) value = value[propA[i]];
          else {
            value = null;
          }
        }
      } else {
        value = row[prop];
      }
      return value;
    },
    tableDataDictionary() {
      if (this.table.form) {
        for (let i in this.table.form.formItemList) {
          if (this.table.form.formItemList[i].dictType) {
            switch (this.table.form.formItemList[i].type) {
              case "select":
              case "switch":
              case "checkbox":
              case "cascader":
              case "radio":
                if (this.table[this.table.form.formItemList[i].dictType] &&
                  this.table[this.table.form.formItemList[i].dictType]
                    .length === 0
                ) {
                  this.table[this.table.form.formItemList[i].dictType] = [
                    { name: "请选择", value: "" },
                  ];
                  this.table.form.formItemList[i].options = [
                    { name: "请选择", value: "" },
                  ];
                  this.getRequestDictList(
                    this.table.form.formItemList[i].dictType,
                    i,
                    this.table.form.formItemList[i]
                  );
                }
                // 新增currentDictType数据字典字段
                if ( this.table[this.table.form.formItemList[i].currentDictType] && 
                    this.table[this.table.form.formItemList[i].currentDictType]
                        .length === 0
                ) {
                  this.table[this.table.form.formItemList[i].currentDictType] = [
                    { name: "请选择", value: "" },
                  ];
                  this.getRequestDictList(
                      this.table.form.formItemList[i].currentDictType,
                      i,
                      this.table.form.formItemList[i]
                  );
                }
                break;
            }
          }
          if (this.table.form.formItemList[i].fun) {
            switch (this.table.form.formItemList[i].type) {
              case "select":
              case "switch":
              case "checkbox":
              case "cascader":
              case "radio":
                if (
                  this.table[this.table.form.formItemList[i].fun].length === 0
                ) {
                  this.table[this.table.form.formItemList[i].fun] = [
                    { name: "", value: "" },
                  ];
                  this.table.form.formItemList[i].options = [
                    { name: "", value: "" },
                  ];
                  this.$emit(this.table.form.formItemList[i].fun, parseInt(i));
                }
                break;
            }
          }
        }
      }
      if (this.table.queryForm) {
        for (let i in this.table.queryForm.formItemList) {
          if (this.table.queryForm.formItemList[i].dictType) {
            switch (this.table.queryForm.formItemList[i].type) {
              case "select":
              case "switch":
              case "checkbox":
              case "cascader":
              case "radio":
                if (this.table[this.table.queryForm.formItemList[i].dictType] &&
                  this.table[this.table.queryForm.formItemList[i].dictType]
                    .length === 0
                ) {
                  this.table[this.table.queryForm.formItemList[i].dictType] = [
                    { name: "请选择", value: "" },
                  ];
                  this.table.queryForm.formItemList[i].options = [
                    { name: "请选择", value: "" },
                  ];
                  this.getRequestDictList(
                    this.table.queryForm.formItemList[i].dictType,
                    i,
                    this.table.queryForm.formItemList[i]
                  );
                }
                  // 新增currentDictType数据字典字段
                if (this.table[this.table.queryForm.formItemList[i].currentDictType] &&
                    this.table[this.table.queryForm.formItemList[i].currentDictType]
                        .length === 0
                ) {
                  this.table[this.table.queryForm.formItemList[i].currentDictType] = [
                    { name: "请选择", value: "" },
                  ];
                  this.getRequestDictList(
                      this.table.queryForm.formItemList[i].dictType,
                      i,
                      this.table.queryForm.formItemList[i]
                  );
                  this.getRequestDictList(
                      this.table.queryForm.formItemList[i].currentDictType,
                      i,
                      this.table.queryForm.formItemList[i]
                  );
                }
                break;
            }
          }
          if (this.table.queryForm.formItemList[i].fun) {
            switch (this.table.queryForm.formItemList[i].type) {
              case "select":
              case "switch":
              case "checkbox":
              case "cascader":
              case "radio":
                if (
                  this.table[this.table.queryForm.formItemList[i].fun]
                    .length === 0
                ) {
                  this.table[this.table.queryForm.formItemList[i].fun] = [
                    { name: "", value: "" },
                  ];
                  this.table.queryForm.formItemList[i].options = [
                    { name: "", value: "" },
                  ];
                  this.$emit(
                    this.table.queryForm.formItemList[i].fun,
                    parseInt(i)
                  );
                }
                break;
            }
          }
        }
      }
      for (let i in this.table.tr) {
        if (this.table.tr[i].dictType) {
          if (this.table[this.table.tr[i].dictType] &&
            this.table[this.table.tr[i].dictType].length === 0) {
            this.table[this.table.tr[i].dictType] = [
              { name: "请选择", value: "" },
            ];
            this.getRequestDictList(
              this.table.tr[i].dictType,
              null,
              this.table.tr[i]
            );
          }
        }
        // 新增currentDictType数据字典字段
        if (this.table.tr[i].currentDictType) {
          if (this.table[this.table.tr[i].currentDictType] &&
            this.table[this.table.tr[i].currentDictType].length === 0) {
            this.table[this.table.tr[i].currentDictType] = [
              { name: "请选择", value: "" },
            ];
            this.getRequestDictList(
                this.table.tr[i].currentDictType,
                null,
                this.table.tr[i]
            );
          }
        }
        if (this.table.tr[i].fun) {
          if (this.table[this.table.tr[i].fun] &&
            this.table[this.table.tr[i].fun].length === 0) {
            this.table[this.table.tr[i].fun] = [{ name: "", value: "" }];
            this.$emit(this.table.tr[i].fun, this.table.tr[i].fun);
          }
        }
      }
    },
    getDefaultData() {
      this.table.listFormModul = {};
      if(this.table.form.formItemList.length>0) {
        this.table.form.formItemList.forEach(item => {
          if(item.type=="date" && item.modelValue) {
            if(item.dateType=='currTime') {
              let curFormat = item.valueFormat.replaceAll('Y','y').replaceAll('D','d')
              this.table.listFormModul[item.key] = this.util.getNow(curFormat, true)
            } else {
              this.table.listFormModul[item.key] = item.modelValue;
            }
          }
        })
      }
      if (this.table.form) {
        for (let i in this.table.form.formItemList) {
          if (!this.table.form.formItemList[i].disable)
            this.table.form.formItemList[i].disable = false;
          let defaultValue = this.table.form.formItemList[i].defaultValue;
          switch (this.table.form.formItemList[i].type) {
            case "checkbox":
            case "cascader":
              this.table.listFormModul[this.table.form.formItemList[i].key] =
                defaultValue === undefined ? [] : defaultValue;
              break;
            case "upload":
            case "sbUpload":
              this.table.listFormModul[this.table.form.formItemList[i].key] =
                defaultValue === undefined ? [] : defaultValue;
              //this.table.form.formItemList[i].filelist=this.table.listFormModul[this.table.form.formItemList[i].key];
              //this.table.form.formItemList[i].uploadData='uploadData';
              break;
            case "qrcode":
              this.table.listFormModul[this.table.form.formItemList[i].key] =
                defaultValue === undefined ? "" : defaultValue;
              //this.table.form.formItemList[i].text=this.table.listFormModul[this.table.form.formItemList[i].key];
              break;
            case "date":
            case "time":
              this.table.listFormModul[this.table.form.formItemList[i].key] =
                defaultValue === undefined ? "" : defaultValue;
              if (this.table.form.formItemList[i].rangeName) {
                let defNow = this.util.getNow("yyyy-MM-dd hh:mm:ss", true);
                this.table.listFormModul[this.table.form.formItemList[i].key] =
                  defaultValue === undefined ? [defNow, defNow] : defaultValue;
                for (let j in this.table.form.formItemList[i].rangeName) {
                  if (this.table.form.formItemList[i].defaultValue) {
                    this.table.listFormModul[
                      this.table.form.formItemList[i].rangeName[j]
                    ] = this.table.form.formItemList[i].defaultValue[j];
                  } else {
                    this.table.listFormModul[
                      this.table.form.formItemList[i].rangeName[j]
                    ] = this.util.getDateFormat(
                      defNow,
                      this.table.form.formItemList[i].type === "time"
                        ? "hh:mm:ss"
                        : this.table.form.formItemList[i].valueFormat ||
                            "yyyy-MM-dd hh:mm:ss"
                    );
                  }
                }
              }
              break;
            default:
              this.table.listFormModul[this.table.form.formItemList[i].key] =
                defaultValue === undefined ? "" : defaultValue;
              break;
          }
        }
      }
    },
    handleAddList(item) {
      this.getDefaultData();
      this.table.listFormModul.read = false;
      if (item.beforeFun) {
        if (this.onOk) {
          let n = this.onOk(item, "beforeFun");
          if (n) {
            ++this.listFormL;
            this.table.dialogTitle =
              "新增" + this.table.modulName.split("-")[1];
            this.table.dialogVisible = true;
            let fn = this.table.modulName.split("-")[0];
          }
        } else {
          ++this.listFormL;
          this.table.dialogTitle = "新增" + this.table.modulName.split("-")[1];
          this.table.dialogVisible = true;
          let fn = this.table.modulName.split("-")[0];
          let n = this.$emit(item.beforeFun, item);
        }
      } else {
        ++this.listFormL;
        this.table.dialogTitle = "新增" + this.table.modulName.split("-")[1];
        this.table.dialogVisible = true;
        let fn = this.table.modulName.split("-")[0];
      }
    },
    handleConfirm(formName, item) {
      this.$refs[formName].$children[0].validate((valid) => {
        if (valid) {
          if (
            this.table.dialogTitle ===
            "新增" + this.table.modulName.split("-")[1]
          ) {
            for (let i in this.table.operation.data) {
              if (this.table.operation.data[i].id === "add")
                this.$emit(this.table.operation.data[i].fun, { params: item });
            }
          } else {
            for (let i in this.table.operation.data) {
              if (this.table.operation.data[i].id === "update")
                this.$emit(this.table.operation.data[i].fun, { params: item });
            }
          }
        } else {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        }
      });
    },
    handleSortChange(obj) {
      //排序事件
      // console.log(JSON.stringify(obj.column), obj.prop, obj.order);
      this.table.listQuery.properties = obj.prop;
      this.table.listQuery.direction =
        obj.order == "descending" ? "desc" : "asc";
      this.handleSearchList();
    },
    handleSelectionChange(val) {
      //选择事件
      if (
        (!this.table.mulitple || this.table.mulitple === true) &&
        this.table.mulitple !== false
      ) {
        //多选
        this.table.multipleSelection = val;
      } else {
        //单选
        for (let i in this.table.multipleSelection) {
          this.$refs.SbElTable.toggleRowSelection(
            this.table.multipleSelection[i],
            true
          );
        }
      }
    },
    handleRowDblClick(row, column, event) {
      //if((!this.table.mulitple || this.table.mulitple===true) && this.table.mulitple!==false){//多选
      //    let index=this.table.multipleSelection.findIndex(ri => ri[this.table.rowKey || "id"]===row[this.table.rowKey || "id"]);
      //    this.handleToggleRowSelection(row,index>-1?false:true);
      //}else{//单选
      //    let index=this.table.multipleSelection.findIndex(ri => ri[this.table.rowKey || "id"]===row[this.table.rowKey || "id"]);
      //    this.handleToggleRowSelection(row,index>-1?false:true);
      //    if(index>-1){
      //        this.table.multipleSelection=[];
      //    }else{
      //        this.table.multipleSelection=[row];
      //    }
      //    this.$refs.SbElTable.clearSelection();
      //}
      if (
        !column ||
        column.type === "selection" ||
        column.columnKey === "operation" ||
        column.type === "expand"
      ) {
        return;
      }
      const data = {
        row,
        event,
        column,
      };
      this.$emit("handleRowDblClick", data);
    },
    handleSelect(val, row) {
      if (
        (!this.table.mulitple || this.table.mulitple === true) &&
        this.table.mulitple !== false
      ) {
        //多选
      } else {
        //单选
        var index = val.findIndex(
          (ri) =>
            ri[this.table.rowKey || "id"] === row[this.table.rowKey || "id"]
        );
        if (index > -1) {
          this.table.multipleSelection = [row];
        } else {
          this.table.multipleSelection = [];
        }
        this.$refs.SbElTable.clearSelection();
      }
    },
    handleClearSelection() {
      this.$refs.SbElTable.clearSelection();
    },
    handleToggleRowSelection(row, checked) {
      this.$refs.SbElTable.toggleRowSelection(row, checked);
    },
    handleSelectAll(val) {
      //单选选择事件
      if (
        (!this.table.mulitple || this.table.mulitple === true) &&
        this.table.mulitple !== false
      ) {
        //多选
      } else {
        //单选
        this.table.multipleSelection = [];
        this.$refs.SbElTable.clearSelection();
      }
    },
    handleOperation(index, row, id, name) {
      const data = this.table[name || "operation"].data;
      for (let i in data) {
        if (id === data[i].id) {
          if (id === "update") {
            ++this.listFormL;
            this.table.dialogTitle =
              "修改" + this.table.modulName.split("-")[1];
            this.table.dialogVisible = true;
            row.read = false;
            // if(data[i].beforeFun){
            //     this.$emit(data[i].beforeFun);
            // }
            this.table.operation.nowBtn = true;
            this.$emit("updateTableData", { operation: this.table.operation });
            this.$emit(data[i].fun + "GetRow", row);
          } else if (id === "read") {
            ++this.listFormL;
            this.table.dialogTitle =
              "查看" + this.table.modulName.split("-")[1];
            this.table.dialogVisible = true;
            row.read = true;
            this.table.operation.nowBtn = false;
            this.$emit("updateTableData", { operation: this.table.operation });
            this.$emit(data[i].fun, row);
          } else if (id === "delete") {
            this.$confirm(
              "是否要" + (data[i].name || "删除") + "该条数据",
              "提示",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            )
              .then(() => {
                this.$emit(data[i].fun, row, index);
              })
              .catch(() => {});
          } else {
            this.$emit(data[i].fun, { row, index, id });
          }
        }
      }
    },
    handleSizeChange(val) {
      this.table.listQuery.size = val;
      this.handleSearchList();
    },
    handleCurrentChange(val) {
      this.$emit("onPageChange", val);
      this.table.listQuery.page = val;
      this.handleSearchList();
    },
    handleSearchList(type) {
      if (this.table.queryBeforeSubmit) {
        let n = this.onOk(this.table, "queryBeforeSubmit");
        if (n) {
          if (type) this.table.listQuery.page = 1;
          this.doSearchList();
        }
      } else {
        if (type) this.table.listQuery.page = 1;
        this.doSearchList();
      }
    },
    doSearchList() {
      if (this.table.loadFilter) {
        this.table.loading = true;
        let loadData = [];
        let si = (this.table.listQuery.page - 1) * this.table.listQuery.size;
        let endSi = this.table.listQuery.page * this.table.listQuery.size;
        if (this.table.listQuery.prop) {
          let data = [...this.table.loadFilterData];
          if (this.table.listQuery.order === "ascending")
            data.sort(
              (a, b) =>
                a[this.table.listQuery.prop] - b[this.table.listQuery.prop]
            );
          else
            data.sort(
              (a, b) =>
                b[this.table.listQuery.prop] - a[this.table.listQuery.prop]
            );
          for (let i = si; i < endSi; i++) {
            if (data[i]) loadData.push(data[i]);
          }
        } else {
          for (let i = si; i < endSi; i++) {
            if (this.table.loadFilterData[i])
              loadData.push(this.table.loadFilterData[i]);
          }
        }
        this.table.data = loadData;
        this.table.loading = false;
      } else {
        for (let i in this.table.listQuery) {
          if (typeof this.table.listQuery[i] === "string")
            this.table.listQuery[i] = this.table.listQuery[i].trim();
        }
        this.$emit("updateTableData", {
          listQuery: { ...this.table.listQuery },
        });
        this.$emit("getList", this.table.listQuery);
      }
    },
    handleResetSearch() {
      let qobj = this.table.listQuery;
      if (this.table.hasQueryForm) {
        for (let i in this.table.queryForm.formItemList) {
          if (this.table.queryForm.noResetList) {
            if (
              this.table.queryForm.noResetList.indexOf(
                this.table.queryForm.formItemList[i].key
              ) == -1
            ) {
              qobj[this.table.queryForm.formItemList[i].key] =
                this.table.queryForm.formItemList[i].type === "cascader"
                  ? []
                  : null;
              if (
                (this.table.queryForm.formItemList[i].type === "date" ||
                  this.table.queryForm.formItemList[i].type === "time") &&
                this.table.queryForm.formItemList[i].isRange
              ) {
                let range = this.table.queryForm.formItemList[i].rangeName;
                for (var j in range) {
                  qobj[range[j]] = null;
                }
              }
            }
          } else {
            qobj[this.table.queryForm.formItemList[i].key] =
              this.table.queryForm.formItemList[i].type === "cascader"
                ? []
                : null;
            if (
              (this.table.queryForm.formItemList[i].type === "date" ||
                this.table.queryForm.formItemList[i].type === "time") &&
              this.table.queryForm.formItemList[i].isRange
            ) {
              let range = this.table.queryForm.formItemList[i].rangeName;
              for (var j in range) {
                qobj[range[j]] = null;
              }
            }
          }
        }
      }
      this.table.listQuery = qobj;
      // this.table = JSON.parse(JSON.stringify(this.table))
      this.handleSearchList();
    },
    handleBatchOperate() {
      if (this.table.batchOperate.operateType == null) {
        this.$message({
          message: "请选择操作类型",
          type: "warning",
          duration: 1500,
        });
        return;
      }
      if (
        this.table.multipleSelection === null ||
        this.table.multipleSelection.length < 1
      ) {
        this.$message({
          message: "请选择要操作的数据",
          type: "warning",
          duration: 1500,
        });
        return;
      }
      this.$confirm("是否要进行该批量操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let ids = [];
          for (let i in this.table.multipleSelection) {
            ids.push(this.table.multipleSelection[i].id);
          }
          for (let i in this.table.batchOperate.list) {
            if (
              this.table.batchOperate.operateType ===
              this.table.batchOperate.list[i].value
            )
              this.$emit(this.table.batchOperate.list[i].fun, ids);
          }
        })
        .catch(() => {});
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (!this.hasMergeRowOrColumn) {
        return;
      } else {
        this.$emit("onMergeRowOrColumn", {
          row,
          column,
          rowIndex,
          columnIndex,
        });
      }
    },
    // 点击某一行时触发的函数
    // *** 按下左键然后移动鼠标到其它列放开左键，会有报错 -- 优化：添加点击行参数，
    handleRowClick(Row, Event, Column) {
      if (
        !Column ||
        Column.type === "selection" ||
        Column.columnKey === "operation" ||
        Column.type === "expand"
      ) {
        return;
      }
      const data = {
        row: Row,
        event: Event,
        column: Column,
      };
      this.$emit("handleRowClick", data);
    },
    // 当单元格 hover 进入时会触发该事件
    handleCellMouseEnter(Row, Column, Cell, Event) {
      if (
        !Column ||
        Column.type === "selection" ||
        Column.columnKey === "operation" ||
        Column.type === "expand"
      ) {
        return;
      }
      const data = {
        row: Row,
        event: Event,
        column: Column,
        cell: Cell,
      };
      this.tableRowEditId = data.row.id; //确定点击的单元格在哪行
      this.tableColumnEditIndex = data.column.id; //确定点击的单元格在哪列
      this.$emit("handleCellMouseEnter", data);
    },
    // 当单元格 hover 退出时会触发该事件
    handleCellMouseLeave(Row, Column, Cell, Event) {
      if (
        !Column ||
        Column.type === "selection" ||
        Column.columnKey === "operation" ||
        Column.type === "expand"
      ) {
        return;
      }
      const data = {
        row: Row,
        event: Event,
        column: Column,
        cell: Cell,
      };
      this.tableRowEditId = ""; //确定点击的单元格在哪行
      this.$emit("handleCellMouseLeave", data);
    },
    // 行类名的回调函数
    // 在表格数据中添加class字段即为表格行类名，配合css可对表格行中的自定义标签进行样式优化
    rowClassName(rowdata) {
      // 如果表格不是树形结构
      if (!this.table.treeProps) {
        const data = this.table.data;
        let className = data[rowdata.rowIndex].class
          ? data[rowdata.rowIndex].class
          : "";
        if (className.length === 0) {
          return;
        }
        className = className.join(" ");
        return className;
      }
    },
    getRequestDictList(dictName, itemi, item) {
      if (item.from) {
        uumsGetDictList(dictName).then((res) => {
          this.doDictList(dictName, res.data);
        });
      } else {
        getDictList(dictName, item.isPublic).then((res) => {
          this.doDictList(dictName, res.data);
        });
      }
    },
    doDictList(dictName, data) {
      for (var i in data) {
        if (data[i].valueType && data[i].valueType === "int")
          data[i].value = parseInt(data[i].value);
      }
      if (this.table.form) {
        for (let i in this.table.form.formItemList) {
          if (this.table.form.formItemList[i].dictType === dictName) {
            this.table.form.formItemList[i].options = { ...data };
            this.$emit("updateTableData", { form: { ...this.table.form } });
          }
        }
      }
      if (this.table.queryForm) {
        for (let i in this.table.queryForm.formItemList) {
          let qFi = this.table.queryForm.formItemList[i];
          if (qFi.dictType === dictName) {
            if (qFi.type === "select" && !qFi.hasNoChoose) {
              if (
                data.length > 0 &&
                data[0][qFi.props ? qFi.props.value : "value"] !== ""
              ) {
                let chooseD = {};
                chooseD[qFi.props ? qFi.props.value : "value"] = "";
                chooseD[qFi.props ? qFi.props.label : "name"] =
                  qFi.nullText || "请选择";
                data.unshift(chooseD);
              }
            }
            this.table.queryForm.formItemList[i].options = data;
            this.$emit("updateTableData", {
              queryForm: { ...this.table.queryForm },
            });
          }
        }
      }
      this.table[dictName] = data;
      let dictData = {};
      dictData[dictName] = this.table[dictName];
      this.$emit("updateTableData", dictData);
    },
    handleBeforeFun(obj) {
      if (this.onOk && obj.beforeFun) {
        let n = this.onOk(obj);
        return n;
      }
      return true;
    },
    handleInputBtn(data) {
      this.$emit(data.fun, data);
    },
    handleDoFun(obj, fun, data) {
      if (this.onOk) {
        if (obj[fun]) {
          let n = this.onOk(obj, fun, data);
          if (fun === "beforeFun") return n;
        }
      } else {
        if (obj[fun]) {
          this.$emit(obj[fun], { obj, data });
        }
      }
      if (fun === "beforeFun") return true;
    },
    renderHeader(h,data){
      return h("span", [
        h(
            "el-tooltip",
            {
              attrs: {
                class: "item",
                effect: "dark",
                content: data.column.label,
                placement: "top",
              },
            },
            [h("span", data.column.label)]
        ),
      ]);
    },
    editClick(row, field) {
      this.$set(row, field + "Edit", true);
    },
    blurValueInput(row, field) {
      this.$set(row, field + "Edit", false);
      this.$emit("handleCreate", row);
    },
    changeEdit(row, field) {
      setTimeout(() => {
        this.$set(row, field + "Edit", false);
        this.$emit("handleCreate", row);
      }, 100);
    },
    handleFileToSee(info) {
       window.open(process.env.VUE_APP_PROBASEURL+info.downLoadUrl, "_blank")
    },
  },
  created() {
    this.getDefaultData();
    this.tableDataDictionary();
    if (this.table.loadFilter) this.handleSearchList();
    if (this.table.hasSetup) this.handleSetup();
    document.body.ondrop = function (event) {
      event.preventDefault();
      event.stopPropagation();
    };
  },
  watch: {
    "table.queryForm": {
      handler: function (nv, ov) {
        if (nv) {
          for (let i in this.table.queryForm.formItemList) {
            let qFi = this.table.queryForm.formItemList[i];
            if (qFi.type === "select" && qFi.options && !qFi.hasNoChoose) {
              if (
                qFi.options.length > 0 &&
                qFi.options[0][qFi.props ? qFi.props.value : "value"] !== ""
              ) {
                let chooseD = {};
                chooseD[qFi.props ? qFi.props.value : "value"] = "";
                chooseD[qFi.props ? qFi.props.label : "name"] =
                  qFi.nullText || "请选择";
                qFi.options.unshift(chooseD);
              }
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
    "table.total": {
      handler: function (nv, ov) {
        // if(nv){
        if (nv === null || nv === 0) this.hideSinglePage = true;
        else this.hideSinglePage = false;
        //}
      },
      deep: true,
      immediate: true,
    },
    "table.listQuery": {
      handler: function (nv, ov) {},
      deep: true,
      immediate: true,
    },
  },
};
</script>
<style>
.el-table__fixed,
.el-table__fixed-right {
  height: 100% !important;
}
.tableQueryForm .el-form-item--mini.el-form-item,
.tableQueryForm .el-form-item--small.el-form-item {
  margin-bottom: 0px;
  margin-top: 6px;
}
.tableQueryForm .el-form-item--small .el-form-item__content,
.tableQueryForm .el-date-editor.el-input {
  float: left;
}
.tableQueryForm .el-form-item {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  /* align-items: flex-end; */
  align-items: center;
}
.tableQueryForm .el-form-item .el-form-item__label {
  padding-top: 0px;
}
.tableQueryForm .el-input__inner {
  min-width: 186px;
}
.tableQueryForm .inputBtn {
  width: 186px;
}
.tableQueryForm .inputBtn .el-input__inner {
  min-width: 100px;
}
.el-table thead {
  line-height: 16px;
}
.el-table th {
  background: #fafafa;
  color: #333;
}
.table-container {
  margin-top: 10px;
}
.nopadding{
  padding: 0px !important;
}
.el-main {
  padding: 0px 0px 20px 20px;
}
.el-table .cell {  
  white-space: nowrap;  
  overflow: hidden;  
  text-overflow: ellipsis;  
}  
</style>
<style scoped>
.app-container {
  padding: 10px 20px 20px;
}
.optIcon {
  margin: 0 0 0 10px;
}
.filter-container,
/* .tableQueryDD, */
.tableQueryForm .el-form-item {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: flex-end;
}
.tableContent {
    width: 100%;
    display: flex;
    align-items: flex-end;
    /* justify-content: end; */
}
.tableQueryDD {
    /* width: 100%; */
}
.searchForm {
    /* width: calc(100% - 150px); */
}

.fieldLeft {
  width: 500px;
  float: left;
}
.fieldRight {
  width: 260px;
  float: right;
  border-left: 1px solid #e6e6e6;
  padding-left: 18px;
  min-height: 360px;
  max-height: 500px;
  overflow-x: hidden;
  overflow-y: auto;
}
.fieldTit {
  color: #8091a5;
  padding: 10px 0;
}
.field {
  display: inline-block;
  width: 25%;
  vertical-align: top;
  padding: 8px 0;
}
.field .checkbox {
  border: 1px solid #b0bac5;
  background: #fff;
  border-radius: 2px;
  width: 18px;
  height: 18px;
  font-size: 18px;
  float: left;
  line-height: 18px;
  text-align: center;
  color: #fff;
  margin-right: 10px;
}
.field .checkbox.checked {
  color: #fff;
  background: #3582fb;
  border-color: #3582fb;
}
.fieldC {
  display: block;
  background: url("../../assets/images/yd.gif") no-repeat left center;
  clear: both;
  padding: 5px 0 5px 12px;
  font-size: 13px;
}
.fieldC:hover {
  background-color: #eaf2ff;
  cursor: move;
}
.fieldC .icon {
  color: #bfbfbf;
  font-size: 12px;
  margin-right: 8px;
}
::v-deep .el-dialog__body {
    max-height: calc(60vh) !important;
    overflow-y: scroll !important;
}
</style>
