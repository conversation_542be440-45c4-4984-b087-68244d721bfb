<template>
  <header class="website-header">
    <!-- 顶部横幅区域 -->
    <div class="header-banner">
      <div class="banner-content">
        <div class="banner-bg">
          <img src="@/assets/images/top-bg.jpg" alt="河南移动党风廉政视窗" class="banner-image" />
        </div>
      </div>
    </div>

    <!-- 导航菜单区域 -->
    <nav class="header-nav">
      <div class="nav-container">
        <ul class="nav-menu">
          <li class="nav-item" :class="{ active: $route.path === '/' || $route.path === '/home' }">
            <router-link to="/" class="nav-link">首页</router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/info-disclosure') }">
            <router-link to="/info-disclosure" class="nav-link">信息公开</router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/discipline-news') }">
            <router-link to="/discipline-news" class="nav-link">新闻动态</router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/regulations') }">
            <router-link to="/regulations" class="nav-link">法规制度</router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/showcase') }">
            <router-link to="/showcase" class="nav-link">警钟长鸣</router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/culture') }">
            <router-link to="/culture" class="nav-link">廉洁文化</router-link>
          </li>
          <li class="nav-item" @click="gotoReport">
            <div class="nav-link">信息报送</div>
          </li>
           <li class="nav-item">
            <div class="nav-link">数智专栏</div>
          </li>
        </ul>
      </div>
    </nav>
  </header>
</template>

<script>
import {mapState} from "vuex";

let qLUrl = window.location.href.indexOf('*************:8088') !=-1 ? 'http://*************:8088/hnjjwz' : 'http://************:8088/hnjjwz'
export default {
  name: "WebsiteHeader",
  data() {
    return {};
  },
  computed: {
    ...mapState(['user'])
  },
  methods: {
    gotoReport(){
       var url = qLUrl + '/index?appcode=hnjjwz&loginuser='+ this.util.getRsa(this.user.user.username) +'&from=oa'
      window.open(url,'_blank')


    },
  },
};
</script>

<style scoped>
.website-header {
  width: 100%;
  position: relative;
  z-index: 1000;
}

/* 横幅区域 */
.header-banner {
  width: 100%;
  height: 136px;
  position: relative;
  overflow: hidden;
}

.banner-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.banner-bg {
  width: 100%;
  height: 100%;
  position: relative;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.6) 0%, rgba(255, 140, 0, 0.6) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-title {
  color: white;
  font-size: 36px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin: 0;
  text-align: center;
}

/* 导航菜单区域 */
.header-nav {
 background-image: linear-gradient(90deg, 
		#e70800 0%, 
		#fdb900 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
  width: 100%;
  justify-content: space-around;
}

.nav-item {
  position: relative;
  cursor: pointer;
}

.nav-link {
  display: block;
  padding: 15px 25px;
  color: white;
  text-decoration: none;
 font-size: 22px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 0;
  letter-spacing: 3px;
    font-family: "Microsoft Yahei", "\5b8b\4f53";
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item.active .nav-link {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-title {
    font-size: 24px;
  }

  .nav-container {
    padding: 0 15px;
  }

  .nav-menu {
    flex-wrap: wrap;
  }

  .nav-link {
    padding: 12px 15px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .header-banner {
    height: 150px;
  }

  .banner-title {
    font-size: 20px;
  }

  .nav-container {
    padding: 0 15px;
  }

  .nav-link {
    padding: 10px 12px;
    font-size: 13px;
  }
}
</style>
