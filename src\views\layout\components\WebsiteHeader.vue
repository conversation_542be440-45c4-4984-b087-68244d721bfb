<template>
  <header class="website-header">
    <!-- 顶部横幅区域 -->
    <div class="header-banner">
      <div class="banner-content">
        <div class="banner-bg">
          <img src="@/assets/images/top-bg.jpg" alt="河南移动党风廉政视窗" class="banner-image" />
        </div>
      </div>
    </div>

    <!-- 导航菜单区域 -->
    <nav class="header-nav">
      <div class="nav-container">
        <ul class="nav-menu">
          <li class="nav-item" :class="{ active: $route.path === '/' || $route.path === '/home' }">
            <router-link to="/" class="nav-link"></router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/info-disclosure') }">
            <router-link to="/info-disclosure" class="nav-link"></router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/discipline-news') }">
            <router-link to="/discipline-news" class="nav-link"></router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/regulations') }">
            <router-link to="/regulations" class="nav-link"></router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/showcase') }">
            <router-link to="/showcase" class="nav-link"></router-link>
          </li>
          <li class="nav-item" :class="{ active: $route.path.includes('/culture') }">
            <router-link to="/culture" class="nav-link"></router-link>
          </li>
          <li class="nav-item" @click="gotoReport">
            <div class="nav-link"></div>
          </li>
           <li class="nav-item">
            <div class="nav-link"></div>
          </li>
        </ul>
      </div>
    </nav>
  </header>
</template>

<script>
import {mapState} from "vuex";

let qLUrl = window.location.href.indexOf('*************:8088') !=-1 ? 'http://*************:8088/hnjjwz' : 'http://************:8088/hnjjwz'
export default {
  name: "WebsiteHeader",
  data() {
    return {};
  },
  computed: {
    ...mapState(['user'])
  },
  methods: {
    gotoReport(){
       var url = qLUrl + '/index?appcode=hnjjwz&loginuser='+ this.util.getRsa(this.user.user.username) +'&from=oa'
      window.open(url,'_blank')


    },
  },
};
</script>

<style scoped>
.website-header {
  width: 100%;
  position: relative;
  z-index: 1000;
}

/* 横幅区域 */
.header-banner {
  width: 100%;
  height: 136px;
  position: relative;
  overflow: hidden;
}

.banner-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.banner-bg {
  width: 100%;
  height: 100%;
  position: relative;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.6) 0%, rgba(255, 140, 0, 0.6) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-title {
  color: white;
  font-size: 36px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin: 0;
  text-align: center;
}

/* 导航菜单区域 */
.header-nav {
 background-image: linear-gradient(90deg, 
		#e70800 0%, 
		#fdb900 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
  width: 100%;
  justify-content: space-around;
}

.nav-item {
  position: relative;
  cursor: pointer;
  height: 61px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

/* 导航链接基础样式 */
.nav-link {
  display: block;
  width: 100%;
  height: 100%;
  color: transparent; /* 隐藏文字，只显示背景图片 */
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0;
  position: relative;
  /* 确保图片能够正确显示 */
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain; /* 改为contain确保图片完整显示 */
  min-width: 80px; /* 设置最小宽度确保图片有足够空间 */
  min-height: 40px; /* 设置最小高度 */
}

/* 各个导航项的背景图片 */
.nav-item:nth-child(1) .nav-link {
  background-image: url("../../../assets/images/home_text.png");
}

.nav-item:nth-child(2) .nav-link {
  background-image: url("../../../assets/images/xxgk_text.png");
}

.nav-item:nth-child(3) .nav-link {
  background-image: url("../../../assets/images/xwdt_text.png");
}

.nav-item:nth-child(4) .nav-link {
  background-image: url("../../../assets/images/fgzd_text.png");
}

.nav-item:nth-child(5) .nav-link {
  background-image: url("../../../assets/images/jzcm_text.png");
}

.nav-item:nth-child(6) .nav-link {
  background-image: url("../../../assets/images/ljwh_text.png");
}

.nav-item:nth-child(7) .nav-link {
  background-image: url("../../../assets/images/xxbs_text.png");
}

.nav-item:nth-child(8) .nav-link {
  background-image: url("../../../assets/images/szzl_text.png");
}

/* 悬停效果 */
.nav-link:hover {
  transform: scale(1.05); /* 轻微放大效果 */
  filter: brightness(1.2); /* 增加亮度 */
}

.nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 活跃状态样式 */
.nav-item.active .nav-link {
  filter: brightness(1.3); /* 活跃状态更亮 */
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-title {
    font-size: 24px;
  }

  .nav-container {
    padding: 0 15px;
  }

  .nav-menu {
    flex-wrap: wrap;
  }

  .nav-item {
    height: 50px; /* 减小高度 */
  }

  .nav-link {
    min-width: 60px; /* 减小最小宽度 */
    min-height: 30px; /* 减小最小高度 */
    background-size: contain; /* 确保图片完整显示 */
  }
}

@media (max-width: 480px) {
  .header-banner {
    height: 120px; /* 减小横幅高度 */
  }

  .banner-title {
    font-size: 20px;
  }

  .nav-container {
    padding: 0 10px;
  }

  .nav-item {
    height: 45px; /* 进一步减小高度 */
  }

  .nav-link {
    min-width: 50px; /* 进一步减小最小宽度 */
    min-height: 25px; /* 进一步减小最小高度 */
    background-size: contain; /* 确保图片完整显示 */
  }

  /* 小屏幕下可能需要调整导航菜单布局 */
  .nav-menu {
    justify-content: space-between;
  }
}
</style>
