<template>
  <footer class="website-footer">
    <div class="footer-content">
      <div class="footer-info">
        <p class="copyright">©版权所有 中国移动通信集团河南有限公司</p>
        <p class="contact-info"></p>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: "WebsiteFooter",
  data() {
    return {
      reportPhone: "0371-12388-001",
      zipCode: "450000",
    };
  },
  computed: {
    currentYear() {
      return new Date().getFullYear();
    },
  },
};
</script>

<style scoped>
.website-footer {
  background-color: #e60000;
  color: #ffffff;
  padding: 20px 0;
  margin-top: auto;
  width: 100%;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.footer-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.copyright {
  margin: 0;
  font-size: 14px;
  color: #fff;
  font-weight: 500;
}

.contact-info {
  margin: 0;
  font-size: 13px;
  color: #999999;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    padding: 0 15px;
  }

  .copyright {
    font-size: 13px;
  }

  .contact-info {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .website-footer {
    padding: 15px 0;
  }

  .footer-info {
    gap: 6px;
  }

  .copyright {
    font-size: 12px;
  }

  .contact-info {
    font-size: 11px;
  }
}
</style>
