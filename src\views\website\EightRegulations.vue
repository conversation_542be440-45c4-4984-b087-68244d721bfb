<template>
  <div class="website-layout">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="header">
        <img src="../../assets/images/banner2_eight.jpg" alt="" />
      </div>
      <div class="cont">
        <div class="cards-container">
          <div
            class="card"
            :class="{ 'active-card': activeIndex === 0 }"
            @click="selectCard(0)"
          >
            <i class="fa fa-file-text" />
            <div class="card-text">上级精神</div>
          </div>
          <div
            class="card"
            :class="{ 'active-card': activeIndex === 1 }"
            @click="selectCard(1)"
          >
            <i class="fa fa-gavel"></i>
            <div class="card-text">纪法知识</div>
          </div>
          <div
            class="card"
            :class="{ 'active-card': activeIndex === 2 }"
            @click="selectCard(2)"
          >
            <i class="fa fa-bell"></i>
            <div class="card-text">警钟长鸣</div>
          </div>
        </div>
        <div class="list-section">
          <div
            class="list-item"
            v-for="(item, index) in listData"
            :key="index"
            @click="goDetail(item)"
          >
            <span class="bullet">•</span>
            <span class="item-text">{{ item.title }}</span>
            <span class="item-date">{{ item.creationTime }}</span>
          </div>
          <div v-if="listData.length === 0" class="no-data">暂无数据</div>
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <button
            class="page-btn prev"
            :disabled="page === 1"
            @click="changePage(page - 1)"
          >
            上一页
          </button>
          <span class="page-numbers">
            <template v-for="(item, index) in pageNumbers">
              <span
                v-if="item !== '...'"
                class="page-number"
                :class="{ active: page === item }"
                :key="'page-' + index"
                @click="changePage(item)"
              >
                {{ item }}
              </span>
              <span v-else class="page-ellipsis" :key="'ellipsis-' + index"
                >...</span
              >
            </template>
          </span>
          <button
            class="page-btn next"
            :disabled="page >= totalPages"
            @click="changePage(page + 1)"
          >
            下一页
          </button>
          <span class="totla-nums">共{{ totalPages }}页</span>
        </div>
      </div>
    </div>

    <!-- 网站底部 -->
    <website-footer></website-footer>

    <!-- 返回首页按钮 -->
    <div class="back-to-home" @click="goToHome">返回首页</div>
  </div>
</template>

<script>
import WebsiteFooter from "@/views/layout/components/WebsiteFooter";
import { getSanounList } from "@/api/public";

export default {
  name: "WebsiteLayout",
  components: {
    WebsiteFooter,
  },
  data() {
    return {
      activeIndex: 0,
      listData: [],
      page: 1,
      size: 10,
      totalPages: 0,
      showBackToTop: false,
    };
  },
  computed: {
    pageNumbers() {
      const pages = [];
      if (this.totalPages <= 10) {
        for (let i = 1; i <= this.totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (this.page <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i);
          }
          pages.push("...");
          for (let i = this.totalPages - 3; i <= this.totalPages; i++) {
            pages.push(i);
          }
        } else if (this.page >= this.totalPages - 3) {
          for (let i = 1; i <= 4; i++) {
            pages.push(i);
          }
          pages.push("...");
          for (let i = this.totalPages - 4; i <= this.totalPages; i++) {
            pages.push(i);
          }
        } else {
          for (let i = 1; i <= 4; i++) {
            pages.push(i);
          }
          pages.push("...");
          for (let i = this.page - 1; i <= this.page + 1; i++) {
            pages.push(i);
          }
          pages.push("...");
          for (let i = this.totalPages - 3; i <= this.totalPages; i++) {
            pages.push(i);
          }
        }
      }
      return pages;
    },
  },
  methods: {
    async selectCard(index) {
      this.activeIndex = index;
      this.page = 1;
      this.fetchListData();
    },
    async fetchListData() {
      let programaCode = "";
      switch (this.activeIndex) {
        case 0:
          programaCode = "025001";
          break;
        case 1:
          programaCode = "025002";
          break;
        case 2:
          programaCode = "097";
          break;
      }

      try {
        const response = await getSanounList({
          columnId: programaCode,
          page: this.page,
          size: this.size,
        });
        console.log("Fetched data:", response.data);
        this.listData = response.data.content;
        this.totalPages = response.data.totalPages;
      } catch (error) {
        console.error("Error fetching data:", error);
        this.listData = [];
        this.totalPages = 0;
      }
    },
    goToHome() {
      this.$router.push("/");
    },
    changePage(newPage) {
      if (newPage >= 1 && newPage <= this.totalPages) {
        this.page = newPage;
        this.fetchListData();
      }
    },
    goDetail(item) {
      let programaCode = "";
      switch (this.activeIndex) {
        case 0:
          programaCode = "025001";
          break;
        case 1:
          programaCode = "025002";
          break;
        case 2:
          programaCode = "025003";
          break;
      }
      this.$router.push({
        path: "/detail",
        query: { id: item.id, columnId: programaCode, pmInsId: item.pmInsId },
      });
    },
  },
  mounted() {
    this.selectCard(this.activeIndex);
  },
};
</script>

<style scoped>
.website-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 200px); /* 减去头部和底部的高度 */
}
.header {
  height: 300px;
  background: url("../../assets/images/banner_eight.png") no-repeat top center;
  background-size: cover;
  padding: 0;
  display: flex;
  -ms-flex-pack: center;
  justify-content: flex-end;
  flex-direction: column;
  align-items: center;
}
.header img {
  width: 1200px;
  height: 100px;
}

.cont {
  padding-top: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.cards-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 30%;
  height: 90px;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #e60000, #ffcc00);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.card:hover::before {
  opacity: 1;
}

.card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
i {
  font-size: 36px;
  color: #555;
  transition: all 0.3s;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f8f8f8;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  margin-right: 15px;
}
i::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.4) 70%,
    rgba(255, 255, 255, 0) 100%
  );
  opacity: 0.7;
}
.card:hover i {
  color: #e60000;
  background-color: #fff0f0;
  transform: scale(1.05);
  box-shadow: 0 5px 20px rgba(230, 0, 0, 0.15);
}

.active-card {
  background: linear-gradient(to right, #e60000, #ffcc00);
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(230, 0, 0, 0.2);
  color: white;
}

.card-icon {
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 auto 10px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.active-card .card-icon {
  background-color: rgba(255, 255, 255, 0.3);
}
.active-card i {
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
}
.active-card i::after {
  opacity: 0.3;
}

.card-text {
  font-size: 16px;
  font-weight: bold;
}

.active-card .card-text {
  color: white;
}

.list-section {
  background-color: #fff;
  padding: 15px 25px 20px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #999;
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Align items to the top */
  font-size: 14px;
  padding: 10px 10px;
  border-bottom: 1px solid #eee;
  transition: all 0.3s;
  margin-bottom: 5px;
  border-radius: 4px;
  cursor: pointer;
}

.list-item:hover {
  background-color: #f5f5f5;
  color: #e60000;
}

.bullet {
  margin-right: 10px;
  color: red;
  font-size: 18px; /* Adjust bullet size */
  line-height: 1; /* Ensure bullet aligns with text */
}

.item-text {
  flex-grow: 1;
  margin-right: 10px;
  line-height: 1.5; /* Improve readability */
}

.item-date {
  color: #999;
  white-space: nowrap; /* Prevent date from wrapping */
}

.back-to-home {
  position: fixed;
  bottom: 100px;
  right: 20px;
  background: linear-gradient(to right, #e60000, #ffcc00);
  color: white;
  border-radius: 30px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  font-size: 12px;
  z-index: 1000;
  text-align: center;
}

.back-to-top {
  position: fixed;
  bottom: 170px;
  right: 20px;
  background: linear-gradient(to right, #e60000, #ffcc00);
  color: white;
  border-radius: 30px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  z-index: 1000;
  text-align: center;
}

/* Pagination Styles (Copied and adapted from Sannouncement/index.vue) */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  padding: 10px 0;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f44336;
  color: white;
  border-color: #f44336;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

.page-number {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.totla-nums {
  color: #333;
  font-size: 14px;
}

.page-number:hover {
  background: #f44336;
  color: white;
  border-color: #f44336;
}

.page-number.active {
  background: #f44336;
  color: white;
  border-color: #f44336;
}

.page-ellipsis {
  padding: 8px 12px;
  color: #666;
  user-select: none;
}
</style>
