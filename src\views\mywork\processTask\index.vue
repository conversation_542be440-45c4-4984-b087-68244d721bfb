<template>
  <div class="app-container">
    <sb-el-table :table="table" @getList="getList" @handleTodo="handleTodo" @updateTableData="updateTableData" :on-ok="handleDoFun">
      <template v-slot:title="{obj}">
        <span class="toDetail" @click="handleTodo(obj)">{{obj.row.title}}</span>
      </template>
    </sb-el-table>

    <!-- 工单详情 -->
    <el-dialog :title="dialogTitle" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <work-order :key="cKey" :gps="gps" :dialogClose="dialogClose"></work-order>
    </el-dialog>
  </div>
</template>
<script>
import WorkOrder from "@/components/WorkOrder";
import { findProcessTask } from "@/api/process";
import { todoListConfig } from "@/assets/js/list.config.js";
import { hidetodoListConfig } from "@/assets/js/hidelist.config.js";
import { todoListQuery } from "@/assets/js/list.query.config.js";
export default {
  name: "processTask",
  components: { WorkOrder },
  data() {
    return {
      viewD: false,
      dialogTitle: "",

      gps: {
        type: "task",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      table: {
        modulName: "processTask-待办列表", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            { label: "工单标题", key: "title", type: "input" },
            {
              label: "流程类型", key: "pmInstType", type: "select", options: [
                { name: "123", value: "123" },
                { name: "456", value: "456" },
                { name: "789", value: "789" }
              ],
              filterable: true
            }
          ],
        },
        tr: [
          { id: "RECEIPTCODE", label: "流程编号", prop: "RECEIPTCODE", width: 180 },
          { id: "RECEIPTTILE", label: "工单标题", prop: "RECEIPTTILE" },
          { id: "CREATEORGNAME", label: "创建部门", prop: "CREATEORGNAME", width: 150 },
          { id: "CREATEUSERNAME", label: "创建人", prop: "CREATEUSERNAME", width: 90 },
          { id: "STARTTIME", label: "创建时间", prop: "STARTTIME", width: 160 },
          { id: "PREVIOUSASSISTANTNAME", label: "已办理人", prop: "PREVIOUSASSISTANTNAME", width: 90 },
          { id: "TASKCREATETIME", label: "到达时间", prop: "TASKCREATETIME", width: 160 },
          { id: "TASKNAME", label: "当前办理环节", prop: "TASKNAME", width: 160 }
        ],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "80",
          fixed: "right",
          data: [
            { id: "handleTodo", name: "办理", fun: "handleTodo" },
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {
    this.searchConfigInit()
    if (todoListConfig.length > 0) {
      todoListConfig.forEach((item) => {
        const tableInfo = {
          id: item.act_field,
          label: item.showLabel,
          prop: item.act_field,
        }
        this.table.tr.push(tableInfo)
      })
    }
    if (hidetodoListConfig.length > 0) {
      let hideArr = []
      hidetodoListConfig.split(',').forEach((item) => {
        const hidetableInfo = {
          id: item,
        }
        hideArr.push(hidetableInfo)
      })
      // console.log(hideArr)
      const idSet = new Set(hideArr.map(item => item.id));
      this.table.tr = this.table.tr.filter(item => !idSet.has(item.id))
    }
    this.getList();
  },
  methods: {
    searchConfigInit() {
      let arr = todoListQuery || [];
      // console.log(arr)
      arr = arr.filter(item => item.businessType == '0')

      arr.forEach(async (item, index) => {
        let obj = {
          label: item.labelName,
          key: item.fieldName,
          type: Boolean(Number(item.controlType))
            ? "select"
            : "input",
        };
        if (obj.type === "select") {
          const request = await this.getSelectInfo(item.interfaceUrl);
          obj.options =
            request.data &&
            request.data.map((item) => {
              return {
                name: item.name,
                value: item.value,
              };
            });
        }
        this.table.queryForm.formItemList.push(obj);
      });
    },
    getSelectInfo(reqUrl) {
      return request({
        method: "get",
        url: util.toUrl(
          "/" + process.env.VUE_APP_APPCODE + "/" + reqUrl
        ),
        contentType: "application/json;charset=UTF-8",
      });
    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      findProcessTask(listQuery || this.table.listQuery).then((res) => {
        this.table.loading = false;
        this.table.data = res.data.content;
        this.table.total = res.data.totalElements;
      }).catch((err) => {
        this.table.loading = false;
      });
    },

    // 办理
    handleTodo(obj) {
      // 参数
      this.gps = {
        type: "task",
        location: obj.row.TASKDEFKEY,
        pmInsType: obj.row.PMINSTTYPE,
        pmInsId: obj.row.RECEIPTCODE,
        taskId: obj.row.TASKID,
        processInstId: obj.row.PROCESSINSTID,
        processDefinitionId: obj.row.PROCESSDEFINTIONID
      };

      // 工单标题
      var th = this.util.appNameTH(obj.row.PMINSTTYPE);
      this.dialogTitle = th.type + (obj.row.RECEIPTTILE || "") + "-审批";

      this.cKey++;
      this.viewD = true;
    },

    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;
}
::v-deep .el-dialog__title {
  color: black !important;
  font-size: 15.5px;
}
::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: black;
}
</style>