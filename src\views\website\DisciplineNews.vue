<template>
  <div class="discipline-news-page">
    <div class="main-container">
      <div class="news-col">
        <div class="news-header company">
          <div class="news-title-wrap">
            <span class="news-title">公司动态</span>
            <span class="news-title-underline"></span>
          </div>
        </div>
        <div class="news-card-all">
          <div v-for="item in companyNews"  @click="handleNewsClick(item)" :key="item.id" class="news-card">
          <div class="news-date-box">
            <div class="news-date-day">{{ formatDate(item.date, 'day') }}</div>
            <div class="news-date-month">{{ formatDate(item.date, 'month') }}</div>
          </div>
          <div class="news-content-box">
            <div class="news-card-title">{{ item.title }}</div>
            <div class="news-card-summary">{{ item.summary }}</div>
            <div class="news-card-meta">
              <span >发布人：{{ item.source }}</span>
              <span style="margin-left: 0px;">浏览量：{{ item.views }}</span>
            </div>
          </div>
        </div>

        </div>
        
        <div class="pagination-box">
          <el-pagination
            background
            layout="prev, pager, next, jumper, ->, total"
            :total="companyTotal"
            :page-size="pageSize"
            :current-page="companyPage"
            @current-change="handleCompanyPageChange"
          />
        </div>
      </div>
      <div class="news-col">
        <div class="news-header branch">
          <div class="news-title-wrap">
            <span class="news-title">分公司动态</span>
            <span class="news-title-underline"></span>
          </div>
        </div>
        <div class="news-card-all">
          <div v-for="item in branchNews" :key="item.id"  @click="handleNewsClick(item)" class="news-card">
          <div class="news-date-box blue">
            <div class="news-date-day">{{ formatDate(item.date, 'day') }}</div>
            <div class="news-date-month">{{ formatDate(item.date, 'month') }}</div>
          </div>
          <div class="news-content-box">
            <div class="news-card-title">{{ item.title }}</div>
            <div class="news-card-summary">{{ item.summary }}</div>
            <div class="news-card-meta">
              <span >发布人：{{ item.source }}</span>
              <span style="margin-left: 0px;">浏览量：{{ item.views }}</span>
            </div>
          </div>
        </div>

        </div>
        
        <div class="pagination-box">
          <el-pagination
            background
            layout="prev, pager, next, jumper, ->, total"
            :total="branchTotal"
            :page-size="pageSize"
            :current-page="branchPage"
            @current-change="handleBranchPageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getgongsiPage, getfengongsiPage } from '@/api/home';
export default {
  name: "DisciplineNews",
  data() {
    return {
      companyNews: [],
      branchNews: [],
      companyTotal: 0,
      branchTotal: 0,
      companyPage: 1,
      branchPage: 1,
      pageSize: 5,
      loadingCompany: false,
      loadingBranch: false,
    };
  },
  created() {
    this.fetchCompanyNews();
    this.fetchBranchNews();
  },
  methods: {
    async fetchCompanyNews() {
      this.loadingCompany = true;
      try {
        const res = await getgongsiPage({ page: this.companyPage, size: this.pageSize });
        if (res && res.data && res.data.content) {
          this.companyNews = res.data.content.map(item => ({
            id: item.id,
            title: item.title,
            summary:  this.util.extractPlainTextFromRichText(item.mainBody),
            date: item.createdTime,
            source: item.departmentName+item.truename,
            views: item.viewsNumber,
              url: `/detail?id=${item.id}&columnId=004&pmInsId=${item.pmInsId}`,

          }));
          this.companyTotal = res.data.totalElements || 0;
        } else {
          this.companyNews = [];
          this.companyTotal = 0;
        }
      } catch (e) {
        this.companyNews = [];
        this.companyTotal = 0;
      }
      this.loadingCompany = false;
    },
    async fetchBranchNews() {
      this.loadingBranch = true;
      try {
        const res = await getfengongsiPage({ page: this.branchPage, size: this.pageSize });
        if (res && res.data && res.data.content) {
          this.branchNews = res.data.content.map(item => ({
            id: item.id,
          title: item.title,
            summary:  this.util.extractPlainTextFromRichText(item.mainBody),
            date: item.createdTime,
            source: item.departmentName+item.truename,
            views: item.viewsNumber,
            url: `/detail?id=${item.id}&columnId=013001&pmInsId=${item.pmInsId}`,

            
          }));
          this.branchTotal = res.data.totalElements || 0;
        } else {
          this.branchNews = [];
          this.branchTotal = 0;
        }
      } catch (e) {
        this.branchNews = [];
        this.branchTotal = 0;
      }
      this.loadingBranch = false;
    },
    handleCompanyPageChange(val) {
      this.companyPage = val;
      this.fetchCompanyNews();
    },
    handleBranchPageChange(val) {
      this.branchPage = val;
      this.fetchBranchNews();
    },
    formatDate(date, type) {
      const d = new Date(date);
      if (type === 'day') return d.getDate().toString().padStart(2, '0');
      if (type === 'month') return (d.getMonth() + 1) + '月';
      return date;
    },
    // 处理新闻点击
    handleNewsClick(news) {
      if (news.url) {
        this.$router.push(news.url);
      }
    },

  }
};
</script>

<style scoped>
.discipline-news-page {
  min-height: 100vh;
  background: #f5f5f5;
}
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 24px;
  padding: 30px  0;
  min-width: 0;
}
.news-col {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  padding: 18px 18px 0 18px;
  display: flex;
  flex-direction: column;
}
.news-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 18px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}
.news-header.branch {
  /* border-bottom: 3px solid #1876d1; */
}
.news-title-wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}
.news-title {
  font-size: 20px;
  font-weight: bold;
  color: #c90100;
  letter-spacing: 1px;
  position: relative;
  padding-left: 10px;
}
.news-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 22px;
  background: #c90100;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 2px;
}
.news-title-underline {
  width: 40px;
  height: 4px;
  background: #ffc310;
  border-radius: 2px;
  margin-top: 2px;
  margin-left: 10px;
}
.news-more {
  color: #999;
  font-size: 15px;
  text-decoration: none;
  transition: color 0.2s;
  margin-left: 10px;
}
.news-more:hover {
  color: #c90100;
}
.news-card {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  margin-bottom: 18px;
  padding: 28px 24px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  transition: box-shadow 0.2s;
  min-height: 180px;
  overflow: hidden;
  min-width: 0;
}
.news-card.blue {
  background: #f7faff;
}
.news-card:hover {
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
}
.news-date-box {
  width: 70px;
  text-align: center;
  margin-right: 18px;
}
.news-date-box.blue .news-date-day,
.news-date-box.blue .news-date-month {
  background: #1876d1;
}
.news-date-day {
  font-size: 26px;
  font-weight: bold;
  color: #fff;
  background: #c90100;
  border-radius: 6px 6px 0 0;
  padding: 6px 0 2px 0;
}
.news-date-month {
  font-size: 14px;
  color: #fff;
  background: #c90100;
  border-radius: 0 0 6px 6px;
  padding: 2px 0 6px 0;
}
.news-content-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
  overflow: hidden;
}
.news-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}
.news-card-summary {
  color: #666;
  font-size: 15px;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  min-width: 0;
}
.news-card-meta {
  color: #999;
  font-size: 13px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.news-card-meta span{
  display: block;
}
.pagination-box {
  margin: 24px 0 16px 0;
  text-align: center;
}
.pagination-box >>> .el-pagination .el-pager li.active {
  background-color: #c90100 !important;
  border-color: #c90100 !important;
  color: #fff !important;
}
.pagination-box >>> .el-pagination .el-pager li:hover {
  color: #c90100 !important;
  border-color: #c90100 !important;
}
.pagination-box >>> .el-pagination .el-pager li.active:hover {
   color: #fff !important;

}
.pagination-box >>> .el-pagination .el-pagination__jump .el-input__inner {
  border-color: #c90100 !important;
}
.pagination-box >>> .el-pagination .el-pagination__editor.el-input .el-input__inner:focus {
  border-color: #c90100 !important;
}
.pagination-box >>> .el-pagination .btn-next, 
.pagination-box >>> .el-pagination .btn-prev {
  color: #c90100 !important;
}
.pagination-box >>> .el-pagination .el-pagination__total {
  color: #c90100 !important;
}
.news-card-all{
  flex: 1;
}
</style> 