<template>
  <div class="xfjb-container">
    <div class="tabs-container">
      <div class="tab-item" :class="{ active: activeTab === 'guide' }" @click="activeTab = 'guide'">
        <i class="fa fa-compass tab-icon"></i>
        <p>举报指南</p>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'chart' }" @click="activeTab = 'chart'">
        <i class="fa fa-desktop tab-icon"></i>
        <p>举报渠道</p>
      </div>
        <div class="tab-item" :class="{ active: activeTab === 'ydt' }" @click="activeTab = 'ydt'">
        <i class="fa fa-image tab-icon"></i>
        <p>信访举报导引图</p>
      </div>
    </div>

    <div class="content-container">
      <div v-if="activeTab === 'guide'" class="guide-content">
        <div class="guide-header">
          <div class="guide-title-bg">
            举报指南
          </div>
          <div class="guide-line"></div>
        </div>
        <div class="section">
          <h3>一、受理原则</h3>
          <p>根据有关规定，依据“分级负责”原则，请举报人按照被举报人属地、级别和干部管理权限，选择相应的举报渠道进行举报。中央纪委国家监委主要受理反映集团公司党组、领导班子成员的纪检监察信访举报；集团公司纪检监察组主要受理反映集团公司总部部门及领导班子成员、河南公司党委及领导班子成员、河南公司纪委及纪委办公室主任和副主任的纪检监察信访举报；河南公司纪委受理反映除上述党组织、人员以外本单位其他党组织、人员的纪检监察信访举报，越级举报、多头举报的将按照上述“分级负责”原则转相应单位处理。为提高办理效率，请举报人按照上述受理原则进行举报。</p>
        </div>
        <div class="section">
          <h3>二、受理范围</h3>
          <p>(一) 对党组织、党员违反政治纪律、组织纪律、廉洁纪律、群众纪律、工作纪律、生活纪律等党的纪律行为的检举控告。</p>
          <p>(二) 对监察对象（监察法规定的六类公职人员，下同）不依法履职，违反秉公用权、廉洁从政从业以及道德操守等规定，涉嫌贪污贿赂、滥用职权、玩忽职守、权力寻租、利益输送、徇私舞弊以及浪费国家资财等职务违法犯罪行为的检举控告。</p>
          <p>(三) 党员对党纪处分或者纪律检查机关所作的其他处理不服，提出的申诉。</p>
          <p>(四) 监察对象对监察机关涉及本人的处理决定不服，提出的申诉；被调查人及其近亲属对监察机关及其工作人员违反法律法规、侵害被调查人合法权益的行为，提出的申诉。</p>
          <p>(五) 对党风廉政建设和反腐败工作的批评建议。</p>
        </div>
        <div class="section">
          <h3>三、不予受理范围</h3>
          <p>(一) 已经或者依法应当通过诉讼、仲裁、行政裁决、行政复议等途径解决的。</p>
          <p>(二) 依照有关规定，属于其他机关或者单位职责范围的。</p>
          <p>(三) 仅列举出违纪或者职务违法、职务犯罪行为名称，无实质内容的。</p>
          <p><strong>特别提示：</strong>对于不属于纪检监察机构受理范围的信访举报，按照相关规定不予受理；信访人实名举报的，纪检监察机构告知其正常举报渠道。</p>
        </div>
        <div class="section">
          <h3>四、倡导实名举报</h3>
          <p>为便于纪检监察机构对举报事项作进一步了解，及时准确掌握情况和反馈处理结果，提倡、鼓励向纪检监察机构实名举报，举报时请写明真实姓名、单位、身份证号和准确联系方式等内容。对实名举报的，将优先办理、优先处置、给予反馈。</p>
        </div>
        <div class="section">
          <h3>五、重视举报质量</h3>
          <p>为提升信访举报件办理质效，举报材料要实事求是、内容具体、简明扼要，切忌内容空泛、道听途说、凭空想象。其中在被反映人（单位）信息方面，写明姓名、单位、职务信息，如反映多名被举报人违纪违法犯罪问题，请分别举报；在主要问题方面，分段书写每个涉嫌违纪违法犯罪的事实，内容应尽量涵盖时间、地点、涉及人员、违纪违法犯罪行为、涉及金额（物品）、主要证据、知情人等信息。</p>
        </div>
        <div class="section">
          <h3>六、避免诬陷不实</h3>
          <p>按照有关规定，检举、控告、申诉人在检举、控告、申诉活动中必须对所检举、控告、申诉的事实的真实性负责。接受调查、询问时，应如实提供情况和证据。如有诬陷、制造假证行为，承担纪律和法律责任。</p>
        </div>
      </div>

      <div v-if="activeTab === 'chart'" class="chart-content">
        <div class="guide-header">
          <div class="guide-title-bg">
            举报渠道
          </div>
          <div class="guide-line"></div>
        </div>
        <div class="sub-nav-chart">
          <button :class="{ active: activeChartTab === 'central' }" @click="activeChartTab = 'central'">中央纪委</button>
          <button :class="{ active: activeChartTab === 'group' }" @click="activeChartTab = 'group'">集团公司</button>
          <button :class="{ active: activeChartTab === 'province' }" @click="activeChartTab = 'province'">省公司</button>
          <button :class="{ active: activeChartTab === 'city' }" @click="activeChartTab = 'city'">地市分公司</button>
        </div>

        <div class="chart-dynamic-content-wrapper" :style="{ height: wrapperHeight + 'px' }">
          <div v-show="activeChartTab === 'central'" class="central-content" ref="centralContent">
            <p>网站： <a href="http://www.12388.gov.cn/" target="_blank">http://www.12388.gov.cn/</a></p>
            <p>电话： 010-12388</p>
            <p>收信单位： 中央纪委国家监委信访室，邮编： 100813</p>
            <p>来访接待地址： 北京市西城区永定门内西街甲2号</p>
          </div>

          <div v-show="activeChartTab === 'group'" class="group-content" ref="groupContent">
            <p>电话: 010-52616186</p>
            <p>收信单位: 北京市西城区金融大街29号中国移动A座纪检监察组，邮编: 10033</p>
            <p>来访接待地址: 北京市西城区金融大街29号中国移动B座</p>
          </div>

          <div v-show="activeChartTab === 'province'" class="province-content" ref="provinceContent">
            <p>电话: 0371-68588019</p>
            <p>收信单位: 郑州市经三路48号河南移动纪委办公室，邮编: 450008</p>
            <p>来访接待地址: 郑州市经三路48号</p>
          </div>

          <div v-show="activeChartTab === 'city'" ref="cityContent">
            <table>
              <thead>
                <tr>
                  <th>序号</th>
                  <th>地市</th>
                  <th>举报电话</th>
                  <th>邮政编码</th>
                  <th>来信来访地址</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>1</td>
                  <td>商丘分公司</td>
                  <td>13781510770</td>
                  <td>476100</td>
                  <td>商丘市睢阳区南京路东段266号 商丘移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>2</td>
                  <td>郑州分公司</td>
                  <td>13598891603</td>
                  <td>450011</td>
                  <td>郑州市金水区北环路11号七楼 郑州移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>3</td>
                  <td>安阳分公司</td>
                  <td>13526113127</td>
                  <td>455000</td>
                  <td>安阳市文峰区中华路南段20号安阳移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>4</td>
                  <td>新乡分公司</td>
                  <td>18337330039</td>
                  <td>453000</td>
                  <td>新乡市红旗区新中大道666号新乡移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>5</td>
                  <td>许昌分公司</td>
                  <td>18803998860</td>
                  <td>461000</td>
                  <td>许昌市八一东路1786号许昌移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>6</td>
                  <td>平顶山分公司</td>
                  <td>17837681001</td>
                  <td>467000</td>
                  <td>平顶山市长安大道与翠竹路交叉口平顶山移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>7</td>
                  <td>信阳分公司</td>
                  <td>13903762016</td>
                  <td>464000</td>
                  <td>信阳市羊山新区新七大道68号信阳移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>8</td>
                  <td>南阳分公司</td>
                  <td>13937700371</td>
                  <td>473000</td>
                  <td>南阳市张衡东路1266号南阳移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>9</td>
                  <td>开封分公司</td>
                  <td>15203781234</td>
                  <td>475000</td>
                  <td>开封市郑开大道与八大街交叉口西南角移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>10</td>
                  <td>洛阳分公司</td>
                  <td>18338812388</td>
                  <td>471000</td>
                  <td>洛阳市洛龙区开元大道242号 洛阳移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>11</td>
                  <td>焦作分公司</td>
                  <td>15239187690</td>
                  <td>454100</td>
                  <td>焦作市迎宾路1373号焦作移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>12</td>
                  <td>鹤壁分公司</td>
                  <td>13803820198</td>
                  <td>458030</td>
                  <td>鹤壁市淇滨区淇水大道与柳江路交汇处东北角移动公司党建工作部（党委宣传部/纪委办公室）</td>
                </tr>
                <tr>
                  <td>13</td>
                  <td>濮阳分公司</td>
                  <td>13939344369</td>
                  <td>457000</td>
                  <td>濮阳市卫河路36号濮阳移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>14</td>
                  <td>周口分公司</td>
                  <td>13526261694</td>
                  <td>466000</td>
                  <td>周口市川汇区文昌路与腾飞路交叉口周口移动纪委办公室</td>
                </tr>
                <tr>
                  <td>15</td>
                  <td>漯河分公司</td>
                  <td>15039612388</td>
                  <td>462000</td>
                  <td>漯河市湘江路23号 漯河移动公司党工作部（党委宣传部/纪委办公室）</td>
                </tr>
                <tr>
                  <td>16</td>
                  <td>驻马店分公司</td>
                  <td>13703969155</td>
                  <td>463000</td>
                  <td>驻马店市开源大道139号驻马店移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>17</td>
                  <td>三门峡分公司</td>
                  <td>18203980002</td>
                  <td>472000</td>
                  <td>三门峡市陕州区迎宾大道与中心大道交叉口西南角 三门峡移动公司纪委办公室</td>
                </tr>
                <tr>
                  <td>18</td>
                  <td>济源分公司</td>
                  <td>18803900050</td>
                  <td>459000</td>
                  <td>济源市黄河大道82号 济源移动公司纪委办公室</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div v-if="activeTab === 'ydt'" class="guide-content">
        <div class="guide-header">
          <div class="guide-title-bg">
            信访举报导引图
          </div>
          <div class="guide-line"></div>
        </div>
        <div class="section">
          <img src="@/assets/images/ydt.png" style="width:100%" alt="信访举报导引图">

        </div>

          
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Xfjb',
  data() {
    return {
      activeTab: 'guide', // Default to '举报指南'
      activeChartTab: 'central', // Default to '中央纪委'
    };
  },
};
</script>

<style scoped>
/* Basic Reset */
body, h1, h2, h3, p, table, th, td, ul, li {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Container */
.xfjb-container {
  width: 100%;
  max-width: 1200px; /* Adjust as needed */
  margin: 20px auto;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* Tabs */
.tabs-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 300px;
  padding: 20px 0;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #EEEEEE; /* Initial light gray border */
  border-radius: 8px; /* Slightly rounded corners */
  margin: 0 10px; /* Spacing between tabs */
  color: #666; /* Default text color */
}

.tab-item:hover {
  color: #007bff; /* Keep text color change on hover */
  border-color: #007bff; /* Keep border color change on hover */
}

.tab-item.active {
  background-color: #FEEAEA; /* Light pink/red background */
  border: 1px solid #DD4444; /* Red border */
  color: #333; /* Darker text color for active tab */
}

.tab-item.active .tab-icon {
  color: #2196F3; /* Blue color for icon in active tab */
}
.tab-item.active p {
  color: #DD4444; /* Blue color for icon in active tab */
}

.tab-icon {
  font-size: 80px; /* Adjust size for Font Awesome icons */
  margin-bottom: 8px;
  color: #666; /* Default icon color (same as text) */
}

/* Content */
.content-container {
  padding: 20px;
}

.guide-header {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  position: relative;
}

.guide-title-bg {
  background-image: url(~@/assets/images/bg.jpg);
  background-size: 100% 100%; /* Cover the entire area */
  background-repeat: no-repeat;
  color: #fff; /* White text color */
  padding: 8px 20px;
  height: 40px; /* Adjust height to fit the image */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px; /* Changed font size to 24px */
  font-weight: bold;
  position: relative; /* For z-index */
  z-index: 2;
  margin-left: 20px;
}

.guide-line {
  flex-grow: 1;
  height: 2px;
  background-color: #DD4444; /* Red line */
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.guide-title-tab::before {
  content: '';
  position: absolute;
  left: -10px; /* Adjust to create the triangle shape */
  top: 0;
  width: 0;
  height: 0;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-right: 10px solid #DD4444; /* Red triangle */
}

.guide-title-tab::after {
  content: '';
  position: absolute;
  right: -10px; /* Adjust to create the triangle shape */
  top: 0;
  width: 0;
  height: 0;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 10px solid #DD4444; /* Red triangle */
}

.section {
  margin-bottom: 25px;
  line-height: 1.8;
  color: #555;
}

.section h3 {
  color: #333;
  font-size: 20px;
  margin-bottom: 15px;
  border-left: 4px solid #DD4444; /* Red vertical line */
  padding-left: 10px; /* Add padding for the line */
  font-weight: bold;
}

.section p {
  text-indent: 2em;
  margin-bottom: 10px;
}

/* Chart specific styles */
.chart-content {
  /* No min-height */
}

.chart-dynamic-content-wrapper {
  position: relative; /* Ensure position is relative for contained children */
  transition: height 0.7s ease; /* Smooth transition for height changes, increased duration */
  overflow: hidden; /* Hide overflow during transition */
}

.central-content,
.group-content,
.province-content,
.city-content {
  /* Revert to default static positioning */
  position: static;
  width: auto;
  top: auto;
  left: auto;
}

.chart-content .sub-nav-chart {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.chart-content .sub-nav-chart button {
  background-color: transparent;
  border: 1px solid transparent;
  border-bottom: none;
  padding: 10px 20px;
  margin-right: 5px;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  outline: none;
}

.chart-content .sub-nav-chart button:hover {
  color: #DD4444;
}

.chart-content .sub-nav-chart button.active {
  background-color: #fff;
  border: 1px solid #eee;
  border-bottom: 3px solid #DD4444;
  color: #DD4444;
  font-weight: bold;
  padding-bottom: 8px;
}

.central-content p,
.group-content p,
.province-content p {
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
  line-height: 1.8;
}

.central-content a {
  color: #007bff;
  text-decoration: none;
}

.central-content a:hover {
  text-decoration: underline;
}

.group-content {
  padding: 20px 0;
}

.province-content {
  padding: 20px 0;
}

/* Table styles */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0; /* Revert margin-top from table */
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

th, td {
  border: 1px solid #ddd;
  padding: 12px 15px;
  text-align: center;
  word-break: break-all; /* Ensure long text wraps */
}

th {
  background-color: #f2f2f2;
  color: #333;
  font-weight: bold;
}

tr:nth-child(even) {
  background-color: #f9f9f9;
}

tr:hover {
  background-color: #f1f1f1;
}
</style>
