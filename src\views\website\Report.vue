<template>
  <div class="report-page">
    <div class="main-container">
      <!-- 左侧边栏 -->
      <aside class="sidebar">
        <div class="sidebar-section">
          <h3 class="section-title">信访举报</h3>
          <ul class="sidebar-menu">
            <li class="menu-item active">
              <a href="#" class="menu-link">
                <i class="menu-icon"></i>
                在线举报
              </a>
            </li>
            <li class="menu-item">
              <a href="#" class="menu-link">
                <i class="menu-icon"></i>
                举报指南
              </a>
            </li>
            <li class="menu-item">
              <a href="#" class="menu-link">
                <i class="menu-icon"></i>
                联系方式
              </a>
            </li>
          </ul>
        </div>
      </aside>

      <!-- 主要内容 -->
      <main class="content-area">
        <div class="content-header">
          <h2 class="content-title">信访举报</h2>
        </div>
        
        <div class="content-body">
          <!-- 举报方式 -->
          <section class="report-methods">
            <h3 class="section-title">举报方式</h3>
            <div class="methods-grid">
              <div class="method-card">
                <div class="method-icon">📞</div>
                <h4 class="method-title">电话举报</h4>
                <div class="method-content">
                  <p class="method-number">0371-12388-001</p>
                  <p class="method-time">工作时间：周一至周五 8:30-17:30</p>
                </div>
              </div>

              <div class="method-card">
                <div class="method-icon">✉️</div>
                <h4 class="method-title">邮箱举报</h4>
                <div class="method-content">
                  <p class="method-email"><EMAIL></p>
                  <p class="method-desc">24小时接收举报邮件</p>
                </div>
              </div>

              <div class="method-card">
                <div class="method-icon">📮</div>
                <h4 class="method-title">信件举报</h4>
                <div class="method-content">
                  <p class="method-address">河南省郑州市金水区东风路18号</p>
                  <p class="method-desc">河南移动纪委监察室（收）</p>
                  <p class="method-desc">邮编：450000</p>
                </div>
              </div>

              <div class="method-card">
                <div class="method-icon">💻</div>
                <h4 class="method-title">网络举报</h4>
                <div class="method-content">
                  <p class="method-desc">通过本网站在线举报平台</p>
                  <button class="report-btn">立即举报</button>
                </div>
              </div>
            </div>
          </section>

          <!-- 举报须知 -->
          <section class="report-notice">
            <h3 class="section-title">举报须知</h3>
            <div class="notice-content">
              <div class="notice-item">
                <h4 class="notice-title">举报范围</h4>
                <ul class="notice-list">
                  <li>违反党的纪律的行为</li>
                  <li>违反国家法律法规的行为</li>
                  <li>违反公司规章制度的行为</li>
                  <li>损害公司利益的行为</li>
                  <li>其他违纪违法行为</li>
                </ul>
              </div>

              <div class="notice-item">
                <h4 class="notice-title">举报要求</h4>
                <ul class="notice-list">
                  <li>举报内容应当客观真实，不得故意捏造事实</li>
                  <li>提供具体的违纪违法事实和证据</li>
                  <li>实名举报将得到优先处理</li>
                  <li>恶意举报将承担相应法律责任</li>
                </ul>
              </div>

              <div class="notice-item">
                <h4 class="notice-title">保护措施</h4>
                <ul class="notice-list">
                  <li>严格保护举报人的个人信息</li>
                  <li>严禁泄露举报人身份</li>
                  <li>严禁对举报人进行打击报复</li>
                  <li>对举报人实施保护措施</li>
                </ul>
              </div>
            </div>
          </section>

          <!-- 在线举报表单 -->
          <section class="online-report">
            <h3 class="section-title">在线举报</h3>
            <form class="report-form">
              <div class="form-group">
                <label class="form-label">举报类型 <span class="required">*</span></label>
                <select class="form-control" required>
                  <option value="">请选择举报类型</option>
                  <option value="corruption">贪污腐败</option>
                  <option value="abuse">滥用职权</option>
                  <option value="violation">违规违纪</option>
                  <option value="other">其他问题</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">被举报人姓名 <span class="required">*</span></label>
                <input type="text" class="form-control" placeholder="请输入被举报人姓名" required>
              </div>

              <div class="form-group">
                <label class="form-label">被举报人单位/部门</label>
                <input type="text" class="form-control" placeholder="请输入被举报人所在单位或部门">
              </div>

              <div class="form-group">
                <label class="form-label">举报内容 <span class="required">*</span></label>
                <textarea class="form-control" rows="6" placeholder="请详细描述举报内容，包括时间、地点、具体事实等" required></textarea>
              </div>

              <div class="form-group">
                <label class="form-label">相关证据</label>
                <input type="file" class="form-control" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                <p class="form-help">支持上传图片、PDF、Word文档等格式，单个文件不超过10MB</p>
              </div>

              <div class="form-group">
                <label class="form-label">举报人信息（可选）</label>
                <div class="reporter-info">
                  <input type="text" class="form-control" placeholder="姓名（实名举报将优先处理）">
                  <input type="tel" class="form-control" placeholder="联系电话">
                  <input type="email" class="form-control" placeholder="邮箱地址">
                </div>
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" required>
                  我已阅读并同意《举报须知》，保证举报内容真实有效
                </label>
              </div>

              <div class="form-actions">
                <button type="submit" class="submit-btn">提交举报</button>
                <button type="reset" class="reset-btn">重置表单</button>
              </div>
            </form>
          </section>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
export default {
  name: "ReportPage",
  data() {
    return {};
  },
  methods: {
    handleSubmit() {
      // 处理表单提交
      alert('举报信息已提交，我们将认真核实处理。');
    }
  }
};
</script>

<style scoped>
.report-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 20px;
  padding: 20px;
  min-height: calc(100vh - 280px);
}

/* 左侧边栏 */
.sidebar {
  width: 250px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.sidebar-section {
  padding: 0;
}

.sidebar .section-title {
  background: #dc143c;
  color: white;
  margin: 0;
  padding: 15px 20px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px 8px 0 0;
}

.sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu-item {
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
}

.menu-link:hover {
  background-color: #f8f8f8;
  color: #dc143c;
}

.menu-item.active .menu-link {
  background-color: #fff5f5;
  color: #dc143c;
  font-weight: 500;
}

.menu-icon {
  width: 4px;
  height: 4px;
  background: #dc143c;
  border-radius: 50%;
  margin-right: 8px;
}

/* 主要内容区域 */
.content-area {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-header {
  background: #f8f9fa;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.content-title {
  margin: 0;
  font-size: 24px;
  color: #333;
  font-weight: 600;
}

.content-body {
  padding: 30px;
}

.section-title {
  color: #dc143c;
  font-size: 20px;
  margin: 0 0 20px 0;
  font-weight: 600;
  border-bottom: 2px solid #dc143c;
  padding-bottom: 8px;
}

/* 举报方式 */
.report-methods {
  margin-bottom: 40px;
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.method-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.method-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #dc143c;
}

.method-icon {
  font-size: 32px;
  margin-bottom: 15px;
}

.method-title {
  color: #dc143c;
  font-size: 18px;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.method-content {
  color: #333;
  line-height: 1.6;
}

.method-number,
.method-email,
.method-address {
  font-weight: 600;
  color: #dc143c;
  margin: 0 0 8px 0;
}

.method-time,
.method-desc {
  color: #666;
  font-size: 14px;
  margin: 0 0 5px 0;
}

.report-btn {
  background: #dc143c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 10px;
  transition: all 0.3s ease;
}

.report-btn:hover {
  background: #b91c3c;
}

/* 举报须知 */
.report-notice {
  margin-bottom: 40px;
}

.notice-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.notice-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #dc143c;
}

.notice-title {
  color: #dc143c;
  font-size: 16px;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.notice-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.notice-list li {
  color: #333;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 15px;
  position: relative;
}

.notice-list li::before {
  content: '•';
  color: #dc143c;
  position: absolute;
  left: 0;
}

/* 在线举报表单 */
.online-report {
  margin-bottom: 40px;
}

.report-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
}

.required {
  color: #dc143c;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #dc143c;
  box-shadow: 0 0 0 2px rgba(220, 20, 60, 0.1);
}

.form-help {
  color: #666;
  font-size: 12px;
  margin: 5px 0 0 0;
}

.reporter-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  font-size: 14px;
  cursor: pointer;
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.submit-btn {
  background: #dc143c;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background: #b91c3c;
}

.reset-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  background: #5a6268;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    padding: 15px;
  }
  
  .sidebar {
    width: 100%;
  }
  
  .content-body {
    padding: 20px;
  }
  
  .methods-grid {
    grid-template-columns: 1fr;
  }
  
  .notice-content {
    grid-template-columns: 1fr;
  }
  
  .reporter-info {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
