<template>
  <div class="news-page">
    <div class="main-container">
      <!-- 左侧边栏 -->
      <aside class="sidebar">
        <div class="sidebar-section law-system">
          <div class="law-header">{{ titleHeader }}</div>
          <ul class="law-menu">
            <li
              class="law-menu-item"
              :class="item.columnId == secondCloId ? 'sel-item' : ''"
              v-for="item in leftList"
              :key="item.id"
              @click="handleMenuItemClick(item)"
            >
              {{ item.columnName }}
            </li>
          </ul>
          <div class="law-contact">
            <div class="contact-row">
              <span class="contact-icon"></span>
              电话：0371-68588019
            </div>
            <div class="contact-row">
              <span class="contact-icon"></span>
              收信单位：郑州市经三路48号河南移动纪委办公室
            </div>
             <div class="contact-row">
              <span class="contact-icon"></span>
              邮编：450008
            </div>
             <div class="contact-row">
              <span class="contact-icon"></span>
              来访接待地址：郑州市经三路48号
            </div>
          </div>
        </div>
      </aside>

      <!-- 主要内容 -->
      <main class="content-area">
        <div class="law-content-header">
          <span class="law-tag">{{ title }}</span>
        </div>
        <div class="law-list">
          <div class="law-item" v-for="item in dataList" :key="item.id">
            <div class="law-title" @click="goDetail(item)">
              {{ util.htmlDecode(item.title) }}
            </div>
            <div class="law-desc">{{ util.extractPlainTextFromRichText(item.mainBody) }}</div>
            <div class="law-meta">
              <span class="law-meta-item">{{ item.createdTime }}</span>
              <span class="law-meta-item">发布人：{{ item.departmentName+item.truename }}</span>
              <span class="law-meta-item">浏览量：{{ item.viewsNumber }}</span>
            </div>
          </div>
        </div>
        <!-- 分页 -->
        <div class="pagination">
          <button
            class="page-btn prev"
            :disabled="page === 1"
            @click="changePage(page - 1)"
          >
            上一页
          </button>
          <span class="page-numbers">
            <template v-for="(item, index) in pageNumbers">
              <span
                v-if="item !== '...'"
                class="page-number"
                :class="{ active: page === item }"
                :key="'page-' + index"
                @click="changePage(item)"
              >
                {{ item }}
              </span>
              <span v-else class="page-ellipsis" :key="'ellipsis-' + index"
                >...</span
              >
            </template>
          </span>
          <button
            class="page-btn next"
            :disabled="page >= totalPages"
            @click="changePage(page + 1)"
          >
            下一页
          </button>
          <span class="totla-nums">共{{ totalPages }}页</span>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { getSanouncement, getSanounList } from "@/api/public";
export default {
  name: "NewsPage",
  props: {
    columnId: {
      type: String,
      default: "",
    },
    titleHeader: {
      type: String,
      default: "",
    },
     childId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      leftList: [],
      page: 1,
      size: 10,
      dataList: [],
      secondCloId: "",
      totalPages: 0,
      title: "",
    };
  },
  computed: {
    pageNumbers() {
      const pages = [];
      if (this.totalPages <= 10) {
        // 如果总页数小于等于10，显示所有页码
        for (let i = 1; i <= this.totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 总页数大于10的情况
        if (this.page <= 4) {
          // 当前页靠近开始
          for (let i = 1; i <= 5; i++) {
            pages.push(i);
          }
          pages.push("...");
          for (let i = this.totalPages - 3; i <= this.totalPages; i++) {
            pages.push(i);
          }
        } else if (this.page >= this.totalPages - 3) {
          // 当前页靠近结束
          for (let i = 1; i <= 4; i++) {
            pages.push(i);
          }
          pages.push("...");
          for (let i = this.totalPages - 4; i <= this.totalPages; i++) {
            pages.push(i);
          }
        } else {
          // 当前页在中间
          for (let i = 1; i <= 4; i++) {
            pages.push(i);
          }
          pages.push("...");
          for (let i = this.page - 1; i <= this.page + 1; i++) {
            pages.push(i);
          }
          pages.push("...");
          for (let i = this.totalPages - 3; i <= this.totalPages; i++) {
            pages.push(i);
          }
        }
      }
      return pages;
    },
  },
  created() {
    if(this.childId){
      this.secondCloId = this.childId;

    }else{
      this.secondCloId = this.columnId;

    }
    this.getSanouncement();
    this.getList();
  },
  methods: {
    getSanouncement() {
      getSanouncement({
        columnId: this.columnId,
      }).then((res) => {
        console.log(res);
        if (res.status === 200) {
          this.leftList = res.data?.filter((item) => item.columnLevel!=1);
          const level1Item = res.data.find(
            (item) => {
              if(this.childId){
                return item.columnId == this.childId
              }else{
                return item.columnLevel === 1

              }
            }
          );
          if (level1Item) {
            this.title = level1Item.columnName;
          }
        }
      });
    },
    getList() {
      getSanounList({
        columnId: this.secondCloId,
        page: this.page,
        size: this.size,
      }).then((res) => {
        console.log(res);
        if (res.status === 200) {
          this.dataList = res.data.content;
          // this.dataList = res.data.content.map((item) => {
          //   if (item.mainBody) {
          //     const textarea = document.createElement("textarea");
          //     textarea.innerHTML = item.mainBody;
          //     item.mainBody = textarea.textContent;
          //   }
          //   return item;
          // });
          this.totalPages = res.data.totalPages;
        }
      });
    },
    handleMenuItemClick(item) {
      if(item.isLink){
        window.open(item.linkUrl,'_blank')
      }else{
        this.secondCloId = item.columnId;
        this.title = item.columnName;
        this.page = 1;
        this.getList();
      }
      
    },
    changePage(newPage) {
      if (newPage >= 1 && newPage <= this.totalPages) {
        this.page = newPage;
        this.getList();
      }
    },
    goDetail(item) {
      this.$router.push({
        path: "/detail",
        query: { id: item.id, columnId: this.columnId, pmInsId: item.pmInsId },
      });
    },
  },
};
</script>

<style scoped>
.news-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 20px;
  padding: 20px;
  min-height: calc(100vh - 280px);
}

/* 新法规制度侧边栏样式 */
.sidebar {
  width: 280px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  height: fit-content;
  padding: 0;
}
.law-system {
  padding: 0;
}
.law-header {
  background: linear-gradient(90deg, #f44336 0%, #ff9800 100%);
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  padding: 18px 0 18px 24px;
  border-radius: 10px 10px 0 0;
  margin-bottom: 0;
  letter-spacing: 2px;
}
.law-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  border-bottom: 1px solid #f0f0f0;
}
.law-menu-item {
  padding: 16px 0 16px 24px;
  font-size: 16px;
  color: #222;
  border-top: 1px solid #f0f0f0;
  cursor: pointer;
  background: #fff;
  transition: background 0.2s;
}
.sel-item {
  color: #f44336;
}
.law-menu-item:first-child {
  border-top: none;
}
.law-menu-item:hover {
  background: #fff7e6;
  color: #f44336;
}
.law-contact {
  padding: 18px 24px 12px 16px;
  font-size: 15px;
  color: #444;
}
.contact-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  word-break: break-all;
}
.contact-icon {
  font-size: 18px;
  margin-right: 8px;
  color: #f44336;
}
.law-contact .contact-row:last-child {
  margin-bottom: 0;
}

/* 主要内容区域 */
.content-area {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.law-content-header {
  padding: 24px 24px 0 24px;
}
.law-tag {
  display: inline-block;
  background: #e60000;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 19px;
  padding: 6px 12px;
  margin-bottom: 18px;
}
.law-list {
  padding: 0 24px 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 28px;
}
.law-item {
  background: #fff;
  border-radius: 10px;
  padding: 12px 0;
  transition: box-shadow 0.2s;
  border-top: 1px solid #f0f0f0;
}
.law-title:hover {
  cursor: pointer;
  text-decoration: underline;
}
.law-title {
  font-size: 20px;
  font-weight: bold;
  color: #222;
  margin-bottom: 10px;
}
.law-desc {
  color: #555;
  font-size: 15px;
  margin-bottom: 18px;
  line-height: 1.7;
  display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.law-meta {
  display: flex;
  gap: 28px;
  color: #888;
  font-size: 14px;
  align-items: center;
}
.law-meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}
.icon-date::before {
  content: "\1F4C5"; /* 📅 */
  margin-right: 4px;
  font-size: 15px;
}
.icon-source::before {
  content: "\1F4D6"; /* 📖 */
  margin-right: 4px;
  font-size: 15px;
}
.icon-view::before {
  content: "\1F441"; /* 👁 */
  margin-right: 4px;
  font-size: 15px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: auto;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f44336;
  color: white;
  border-color: #f44336;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

.page-number {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.totla-nums {
  color: #333;
  font-size: 14px;
}

.page-number:hover {
  background: #f44336;
  color: white;
  border-color: #f44336;
}

.page-number.active {
  background: #f44336;
  color: white;
  border-color: #f44336;
}

.page-ellipsis {
  padding: 8px 12px;
  color: #666;
  user-select: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    padding: 15px;
  }

  .sidebar {
    width: 100%;
  }

  .law-content-header,
  .law-list {
    padding-left: 12px;
    padding-right: 12px;
  }
  .law-item {
    padding: 14px 10px 12px 10px;
  }
  .law-title {
    font-size: 16px;
  }
}
</style>
