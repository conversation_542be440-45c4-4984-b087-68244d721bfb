<template>
  <div class="detail-page">
    <div class="detail-container">
      <h1 :class="{'article-title':true,'red':article.importantFlag == 1}">{{ article.title?util.htmlDecode(article.title):'' }}</h1>
      <div class="article-meta">
        <span class="meta-item" v-if="article.articleStartTime"
          >发布时间：{{ article.articleStartTime }}</span
        >
        <span class="meta-item" v-if="article.articleFrom"
          >来源：{{ article.articleFrom }}</span
        >
        <span class="meta-item" v-if="article.viewsNumber">浏览量：{{ article.viewsNumber }}</span>
      </div>
      <div class="article-content" v-html="article.mainBody"></div>

      <!-- 相关文章 -->
      <div class="related-articles" v-if="recommendedArticles.length > 0">
        <div class="related-title">
          <span class="title-icon"></span>
          <h3>相关文章</h3>
        </div>
        <ul class="related-list">
          <li
            class="related-item"
            v-for="item in recommendedArticles"
            :key="item.id"
            @click="goToRelatedDetail(item)"
          >
            <span class="bullet-point"></span>
            <span class="article-link">{{ util.htmlDecode(item.title) }}</span>
            <span class="article-date">{{ formatDate(item.createdTime) }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { getSanounDetail, getSanounRecommend } from "@/api/public";

export default {
  name: "DetailPage",
  data() {
    return {
      article: {},
      recommendedArticles: [], // 存储推荐文章
    };
  },
  created() {
    this.fetchArticleDetail();
    this.fetchRecommendedArticles(); // 调用获取推荐文章的方法
  },
  methods: {
    fetchArticleDetail() {
      const articleId = this.$route.query.id;
      const columnId = this.$route.query.columnId;

      if (articleId && columnId) {
        getSanounDetail({
          columnId: columnId,
          articleId: articleId,
        }).then((res) => {
          console.log("Article Detail:", res);
          if (res.status === 200 && res.data) {
            // 对 mainBody 进行 HTML 实体反转义，以防接口返回转义后的内容
            if (res.data.mainBody) {
              const textarea = document.createElement("textarea");
              textarea.innerHTML = res.data.mainBody;
              res.data.mainBody = textarea.textContent;
            }
            this.article = res.data;
          }
        });
      } else {
        console.error("Missing articleId or columnId in route query.");
      }
    },
    fetchRecommendedArticles() {
      const pmInsId = this.$route.query.pmInsId;
      if (pmInsId) {
        getSanounRecommend({
          pmInsId: pmInsId,
        })
          .then((res) => {
            console.log("Recommended Articles:", res);
            if (res.status === 200 && res.data) {
              this.recommendedArticles = res.data;
            }
          })
          .catch((error) => {
            console.error("Error fetching recommended articles:", error);
          });
      }
    },
    // 点击相关文章跳转
    goToRelatedDetail(item) {
      console.log(item);
      console.log(this.columnId);

      this.$router
        .push({
          path: "/detail",
          query: {
            id: item.id,
            columnId: this.$route.query.columnId,
            pmInsId: item.pmInsId,
          },
        })
        .then(() => {
          this.$router.go(0);
        });
      // 重新加载页面以获取新的详情和推荐文章
    },
    // 格式化日期
    formatDate(date) {
      if (!date) return "";

      let dateObj;
      if (typeof date === "string") {
        // 处理字符串格式的日期
        dateObj = new Date(date);
      } else if (date instanceof Date) {
        dateObj = date;
      } else {
        return "";
      }

      // 检查日期是否有效
      if (isNaN(dateObj.getTime())) {
        return "";
      }

      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, "0");
      const day = String(dateObj.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
  },
};
</script>

<style scoped>
.detail-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.detail-container {
  max-width: 1200px;
  margin: 0 auto;
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.article-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}
.article-title.red {
  color: #c90100; /* 红色 */ 
}

.article-meta {
  text-align: center;
  color: #888;
  font-size: 14px;
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.meta-item {
  margin: 0 10px;
}

.article-content {
  line-height: 1.8;
  color: #555;
  font-size: 16px;
  margin-bottom: 40px; /* 与相关文章部分留出间距 */
}

/* 基本富文本样式覆盖，确保内部 HTML 标签正常显示 */
.article-content >>> p {
  margin-bottom: 1em;
}

.article-content >>> strong,
.article-content >>> b {
  font-weight: bold;
}

.article-content >>> em,
.article-content >>> i {
  font-style: italic;
}

.article-content >>> span {
  /* 根据需要调整 span 的样式 */
}

.article-content >>> table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}

.article-content >>> th,
.article-content >>> td {
  border: 1px solid #ddd;
  padding: 8px;
}

.article-content >>> th {
  background-color: #f2f2f2;
  text-align: left;
}

.article-content >>> img {
  max-width: 100%;
  height: auto;
}

/* 相关文章样式 */
.related-articles {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee; /* 可选：顶部边框 */
}

.related-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 18px;
  background-color: #f44336; /* 红色 */
  margin-right: 8px;
  border-radius: 2px;
}

.related-title h3 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.related-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.related-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px dashed #ddd; /* 虚线 */
  cursor: pointer;
  transition: color 0.2s ease;
}

.related-item:last-child {
  border-bottom: none;
}

.related-item:hover .article-link {
  color: #f44336; /* 悬停变红 */
}

.bullet-point {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #f44336; /* 红色点 */
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0; /* 防止flex item缩小 */
}

.article-link {
  flex-grow: 1; /* 占据剩余空间 */
  margin-right: 10px; /* 与日期留出间距 */
  color: #555;
  font-size: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-date {
  color: #888;
  font-size: 14px;
  flex-shrink: 0; /* 防止flex item缩小 */
}
</style>
