<template>
  <div>
    <Sannouncement v-if="$route.query.columnId!='099'" :columnId="$route.query.columnId" :childId="$route.query.id"  :titleHeader="$route.query.title"></Sannouncement>
    <!-- 公告 -->
    <GongGao v-else :columnId="$route.query.columnId" :childId="$route.query.id"  :titleHeader="$route.query.title"></GongGao>

  </div>
</template>
<script>
import Sannouncement from "@/components/Sannouncement/index.vue";
import GongGao from "@/components/Sannouncement/gonggao.vue";

export default {
  name: "News",
  components: {
    Sannouncement,
    GongGao
  },
  data(){
    return{
    }
  },
  created(){
    
  }
};
</script>
