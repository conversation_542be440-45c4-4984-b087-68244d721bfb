import request from "@/assets/js/request";
import store from "@/store";
import util from "@/assets/js/public";

// 通用新闻接口 - 根据栏目ID获取文章列表
export function getNewsByColumnId(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/newcolumn/findAllArticleByColumnId?columnId=${params.columnId}&page=${params.page}&size=${params.size}`,
        contentType: "application/json;charset=UTF-8",
    });
}
export function constructPetitionLayout(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/templateLayout/constructPetitionLayout`,
        contentType: "application/json;charset=UTF-8",
    });
}

// 获取廉政要闻数据 (columnId: 005)
export function getLianZhengYaoWen(params = {}) {
    return getNewsByColumnId({
        columnId: '005',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}

// 获取工作动态公司数据 (columnId: 001)
export function getGongZuoDongTai(params = {}) {
    return getNewsByColumnId({
        columnId: '004',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 获取工作动态分公司数据 (columnId: 001)
export function getGongZuoDongTaiFen(params = {}) {
    return getNewsByColumnId({
        columnId: '013001',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 党廉工作动态
export function getDangLianGongZuoDongTai(params = {}) {
    return getNewsByColumnId({
        columnId: '013002',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 业务讲堂
export function getYewuJiangTang(params = {}) {
    return getNewsByColumnId({
        columnId: '028',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 纪法课堂
export function getJiFaKetang(params = {}) {
    return getNewsByColumnId({
        columnId: '029',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 警示教育专区
export function getJingShiJiaoYu(params = {}) {
    return getNewsByColumnId({
        columnId: '025',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 公告接口
export function getGongGao(params = {}) {
    return getNewsByColumnId({
        columnId: '099',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 轮播图
export function getCarousel(params = {}) {
    return getNewsByColumnId({
        columnId: '098',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 警钟长鸣
export function getJingZhongChangMing(params = {}) {
    return getNewsByColumnId({
        columnId: '097',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 分页通用
export function getNewsPageByColumnId(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/newcolumn/findArticlePageByColumnId?columnId=${params.columnId}&page=${params.page}&size=${params.size}`,
        contentType: "application/json;charset=UTF-8",
    });
}
// 公司
export function getgongsiPage(params = {}) {
    return getNewsPageByColumnId({
        columnId: '004',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}
// 分公司
export function getfengongsiPage(params = {}) {
    return getNewsPageByColumnId({
        columnId: '013001',
        page: params.page || 1,
        size: params.size || 10,
        ...params
    });
}




// 兼容旧接口名称
export const getLianWenYaoLun = getLianZhengYaoWen;