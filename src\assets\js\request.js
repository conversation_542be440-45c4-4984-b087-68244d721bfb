import Axios from "axios";
import Qs from "qs";
import { Message, MessageBox } from "element-ui";
import store from "../../store";
import router from "../../router";
import { getToken } from "./auth";
import Cookies from "js-cookie";
import { showLoading, hideLoading } from "./loading";

Axios.defaults.withCredentials = true;
Axios.defaults.method = "post";
// TODO 设置超时时间
Axios.defaults.timeout = 0;
Axios.defaults.headers["Access-Control-Allow-Origin"] = "*"; //withCredentials = true;不生效加上这个
Axios.defaults.headers["Access-Control-Allow-Credentials"] = true; //withCredentials = true;不生效加上这个
// Axios.defaults.headers['X-Requested-With']='XMLHttpRequest';

// Axios.defaults.headers = {
//   // 'Access-Control-Allow-Origin': '*',
//   // 'Content-Type': 'application/json'
// }

// TODO http code 校验
Axios.defaults.validateStatus = function (status) {
  return status;
};

// TODO GET 请求 params 序列化
// Axios.defaults.paramsSerializer = function (params) {
//   return Qs.stringify(params)
// }
//service.defaults.baseURL='/api';
// 环境的切换
//if (process.env.NODE_ENV == 'development') {   //开发环境
//    service.defaults.baseURL = process.env.VUE_APP_DEVBASEURL;}
//else if (process.env.NODE_ENV == 'debug') {    //测试环境
//    service.defaults.baseURL = process.env.VUE_APP_DEBBASEURL;
//}
//else if (process.env.NODE_ENV == 'production') {    //生产环境
//    service.defaults.baseURL = process.env.VUE_APP_PROBASEURL;
//}

let cancel,
  promiseArry = {};
//request请求拦截器
Axios.interceptors.request.use(
  config => {
    // if (store.getters.user && store.getters.user.currentCorp) {
    //   config.headers["currentCorp"] = store.getters.user.currentCorp;
    // }
    if (store.getters.token) {
      config.headers["Authorization"] = getToken();
    }
    if (process.env.VUE_APP_AJAXLOCAL === "true") {
      config.method = "get";
      let url = config.url.split("?")[0].split("/");
      //if(typeof config.data ==="string"){
      //if(typeof JSON.parse(config.data)==="object")
      //    config.data=JSON.parse(config.data);
      //else
      //    config.data={"data":config.data};
      //}
      //config.url=`public/ajax/restuumslogin.json`;
      config.data = { url: url, data: config.data };
      config.url = "api/seller";
    }
    if (config.loading) {
      showLoading();
    }
    return config;
  },
  error => {
    //console.log("request请求拦截器error",error);
    if (config.loading) hideLoading();
    Promise.reject(error);
  }
);

//respone响应拦截器
Axios.interceptors.response.use(
  response => {
    let res = {};
    if (typeof response.data === "string") {
      if (response.data.indexOf("parent") > -1)
        res = eval(
          response.data
            .replace(new RegExp("parent", "g"), "window")
            .split('<script type="text/javascript">')[1]
            .split("</script>")[0]
        );
    } else {
      if (response.data.type && response.data.type !== "application/json") {
        //==='text/xml'  && response.data.type!=="application/json"
        let filename = response.headers["content-disposition"];
        if (filename) {
          filename = decodeURI(filename.split("filename=")[1]);
          // filename = filename.substring(1, filename.length - 1);
          if (filename.indexOf("errcode") > -1) {
            res = { errcode: -1, message: filename.split("-")[1], data: null };
          } else {
            res = { errcode: 0, data: response.data, filename };
          }
        } else {
          res = { errcode: 0, data: response.data, filename };
        }
      } else {
        res = response.data;
      }
    }
    //console.log(JSON.stringify(res));
    //console.log("-----------------------------");
    if (process.env.VUE_APP_AJAXLOCAL === "true") {
      let resConfig = JSON.parse(response.config.data);
      let url = resConfig.url;
      res = response.data;
      for (var i in url) {
        if (url[i] !== "" && res[url[i]]) {
          res = res[url[i]];
        }
      }
      if (resConfig.data.dictType) res = res[resConfig.data.dictType];
      console.log(url, res);
    }

    if (response.config.loading) hideLoading();

    if (res.errcode === 0 || res.errcode === 200) {
      if (res.message != undefined && res.message != null) {
        // Message({
        //   message: res.message,
        //   type: "success",
        //   duration: 3000
        // });
      }
      // console.log("respone响应拦截器success",res);
      return Promise.resolve(res);
    } else {
      if (res.message != undefined && res.message != null) {
        Message({
          message: res.message,
          type: "error",
          duration: 3000
        });
      }
      if (res.errcode === 401 || res.errcode === 403) {
        //Message({
        //    message:'返回401',
        //    type:'error',
        //    duration: 3000
        //});
        //MessageBox.confirm('你已被登出,可以取消继续留在该页面,或者重新登录','确认登出',{
        //    confirmButtonText:'重新登录',
        //    cancelButtonText:'取消',
        //    type:'warning'
        //}).then((action) => {
        if (router.history.current.path != "/login") {
          store.dispatch("FedLogOut").then(() => {
            //location.reload();
            router.push({ path: '/login' });
          });
        }
        //});
      }
      // console.log("respone响应拦截器successError",res);
      return Promise.reject(res);
    }
  },
  error => {
    if (resConfig.loading) hideLoading();
    let response = JSON.parse(JSON.stringify(error)).response.data;
    if (response) {
      if (response.message) {
        Message({
          message: response.status + "，" + response.message,
          type: "error",
          duration: 3000
        });
      }
      // console.log("respone响应拦截器error",error);
      return Promise.reject(response);
    } else {
      console.log("断网啦!?");
    }
  }
);
function request(obj) {
  if (!obj.data) obj.data = {};
  if (
    !obj.noCurrent &&
    (process.env.VUE_APP_APPCURRENTUSER === undefined ||
      (process.env.VUE_APP_APPCURRENTUSER != undefined &&
        process.env.VUE_APP_APPCURRENTUSER === "true")) &&
    store.getters.user &&
    store.getters.user.currentCorp
  ) {
    // obj.data.schoolId = store.getters.user.currentBloc;
    // obj.data.zoneId = store.getters.user.currentCorp;
  }
  if (obj.contentType) {
    if (obj.contentType.indexOf("json") > -1) {
      Axios.defaults.headers["Content-Type"] = obj.contentType;
    }
  } else {
    if (obj.data) obj.data = Qs.stringify(obj.data);
    Axios.defaults.headers["Content-Type"] =
      "application/x-www-form-urlencoded;charset=UTF-8";
  }
  if (!obj.method) obj.method = "post";
  return new Promise((resolve, reject) => {
    Axios(obj)
      .then(response => {
        resolve(response);
      })
      .catch(error => {
        // if (obj.catch) reject(error);
        // 做当好规则修改状态时，catch不到异常，所以改为一下写法（之后如果影响到其他接口，可以再改回去）
        if (obj.catch) {
          reject(error)
        } else {
          resolve(error);
        }
      });
  });
  // if(obj.method && obj.method==='get'){
  //   return new Promise((resolve, reject) => {
  //     Axios.get(obj.url, { params: obj.data,responseType: 'blob'})
  //       .then(response => {
  //         resolve(response)
  //       })
  //       .catch(error => {
  //         reject(error)
  //       })
  //   })
  // }else{
  //   return new Promise((resolve, reject) => {
  //     Axios.post(obj.url, obj.data)
  //       .then(response => {
  //         resolve(response)
  //       })
  //       .catch(error => {
  //         reject(error)
  //       })
  //   })
  // }
}
export default request;
